#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
S3 Unified STAC Table Cleanup Script

This script performs comprehensive cleanup of the unified_stac_table in S3:
1. Deduplicates rows based on scene_id + cog_key
2. Optimizes/compacts small files 
3. Vacuums with 30-minute retention window

Usage:
    # Dry run first (recommended)
    uv run scripts/cleanup_s3_unified_table.py --dry-run

    # Full cleanup
    uv run scripts/cleanup_s3_unified_table.py

    # Check stats only
    uv run scripts/cleanup_s3_unified_table.py --stats-only
"""

import argparse
import logging
import sys
import time
from pathlib import Path

# Add the src directory to Python path for imports
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# S3 table configuration
S3_TABLE_PATH = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_table"
VACUUM_RETENTION_HOURS = 1  # 1 hour (minimum supported by Delta Lake)
TARGET_FILE_SIZE = 134217728  # 128MB
ZORDER_COLUMNS = ["s2_cell_id"]  # Z-order on spatial index only (time already partitioned)


def main():
    parser = argparse.ArgumentParser(
        description="Cleanup S3 unified STAC table with deduplication, optimization, and vacuum",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be done without making changes")
    parser.add_argument("--stats-only", action="store_true",
                       help="Only show table statistics, no cleanup")
    parser.add_argument("--skip-dedup", action="store_true",
                       help="Skip deduplication step")
    parser.add_argument("--skip-optimize", action="store_true",
                       help="Skip optimization step")
    parser.add_argument("--skip-vacuum", action="store_true",
                       help="Skip vacuum step")
    parser.add_argument("--vacuum-hours", type=float, default=VACUUM_RETENTION_HOURS,
                       help=f"Vacuum retention hours (default: {VACUUM_RETENTION_HOURS} = 30 minutes)")
    
    args = parser.parse_args()
    
    logger.info("🚀 S3 Unified STAC Table Cleanup")
    logger.info("=" * 50)
    logger.info(f"📍 Table: {S3_TABLE_PATH}")
    logger.info(f"🕐 Vacuum retention: {args.vacuum_hours}h ({args.vacuum_hours * 60:.0f} minutes)")
    logger.info(f"📁 Target file size: {TARGET_FILE_SIZE // (1024*1024)}MB")
    logger.info(f"📊 Z-order columns: {ZORDER_COLUMNS} (excludes partition columns year/month)")
    
    if args.dry_run:
        logger.info("🔍 DRY RUN MODE - No changes will be made")
    
    # Import the cleanup functions from the main script
    try:
        # Import cleanup functions
        import subprocess
        import os
        
        # Get the directory of this script
        script_dir = Path(__file__).parent
        clean_script = script_dir / "clean_delta_table.py"
        
        if not clean_script.exists():
            logger.error(f"❌ Clean script not found: {clean_script}")
            return 1
        
        # Build command arguments
        cmd = ["uv", "run", str(clean_script), S3_TABLE_PATH]
        
        if args.stats_only:
            cmd.append("--stats-only")
        else:
            # Add cleanup operations
            if not args.skip_dedup:
                cmd.append("--deduplicate")
            
            if not args.skip_optimize:
                cmd.extend(["--optimize", "--target-file-size", str(TARGET_FILE_SIZE)])
                cmd.extend(["--zorder-columns", ",".join(ZORDER_COLUMNS)])
            
            if not args.skip_vacuum:
                cmd.extend(["--vacuum-hours", str(args.vacuum_hours)])
        
        if args.dry_run:
            cmd.append("--dry-run")
        
        logger.info(f"🔧 Running command: {' '.join(cmd)}")
        logger.info("")
        
        # Execute the cleanup
        start_time = time.time()
        result = subprocess.run(cmd, cwd=script_dir.parent)
        duration = time.time() - start_time
        
        if result.returncode == 0:
            logger.info("")
            logger.info(f"✅ {'[DRY RUN] ' if args.dry_run else ''}Cleanup completed successfully in {duration:.2f}s")
            
            if not args.dry_run and not args.stats_only:
                logger.info("")
                logger.info("🎉 S3 Unified STAC Table Cleanup Complete!")
                logger.info("📋 Summary of operations performed:")
                if not args.skip_dedup:
                    logger.info("   ✅ Deduplication: Removed duplicate rows")
                if not args.skip_optimize:
                    logger.info("   ✅ Optimization: Compacted small files to 128MB")
                    logger.info("   ✅ Z-order: Applied spatial/temporal ordering")
                if not args.skip_vacuum:
                    logger.info(f"   ✅ Vacuum: Cleaned files older than {args.vacuum_hours}h")
                logger.info("")
                logger.info("🚀 Table is now optimized for query performance!")
        else:
            logger.error(f"❌ Cleanup failed with exit code {result.returncode}")
            return result.returncode
            
    except Exception as e:
        logger.error(f"❌ Error during cleanup: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
