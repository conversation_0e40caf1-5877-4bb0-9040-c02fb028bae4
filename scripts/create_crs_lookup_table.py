#!/usr/bin/env python3
# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Create CRS lookup table in Delta Lake for human-readable CRS name mappings.

This script creates a Delta Lake table with EPSG code to human-readable CRS name mappings.
The table can be updated independently of the codebase to add new CRS codes.

Usage:
    uv run python scripts/create_crs_lookup_table.py --output-path s3://bucket/crs_lookup_table
    uv run python scripts/create_crs_lookup_table.py --output-path ./local/crs_lookup_table
"""

import argparse
import logging
from typing import Dict, List
import pandas as pd

try:
    import deltalake as dl
    from deltalake.writer import write_deltalake
    DELTA_AVAILABLE = True
except ImportError:
    print("❌ deltalake not available. Install with: uv add deltalake")
    DELTA_AVAILABLE = False

from data_marketplace.cog.tiff_mappings import COMMON_CRS_MAPPING

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_extended_crs_mappings() -> Dict[int, str]:
    """
    Get extended CRS mappings including common projections.
    
    Returns:
        Dictionary mapping EPSG codes to human-readable names
    """
    # Start with our common mappings
    extended_mappings = COMMON_CRS_MAPPING.copy()
    
    # Add more UTM zones for global coverage
    for zone in range(1, 61):  # UTM zones 1-60
        # Northern hemisphere
        epsg_n = 32600 + zone
        extended_mappings[epsg_n] = f"WGS 84 / UTM zone {zone}N"
        
        # Southern hemisphere  
        epsg_s = 32700 + zone
        extended_mappings[epsg_s] = f"WGS 84 / UTM zone {zone}S"
    
    # Add common State Plane systems (NAD83)
    state_plane_mappings = {
        # Alaska
        26901: "NAD83 / Alaska zone 1",
        26902: "NAD83 / Alaska zone 2", 
        26903: "NAD83 / Alaska zone 3",
        26904: "NAD83 / Alaska zone 4",
        26905: "NAD83 / Alaska zone 5",
        26906: "NAD83 / Alaska zone 6",
        26907: "NAD83 / Alaska zone 7",
        26908: "NAD83 / Alaska zone 8",
        26909: "NAD83 / Alaska zone 9",
        26910: "NAD83 / Alaska zone 10",
        
        # California
        26941: "NAD83 / California zone 1",
        26942: "NAD83 / California zone 2",
        26943: "NAD83 / California zone 3",
        26944: "NAD83 / California zone 4",
        26945: "NAD83 / California zone 5",
        26946: "NAD83 / California zone 6",
        
        # Texas
        32037: "NAD83 / Texas North",
        32038: "NAD83 / Texas North Central", 
        32039: "NAD83 / Texas Central",
        32040: "NAD83 / Texas South Central",
        32041: "NAD83 / Texas South",
        
        # Florida
        32058: "NAD83 / Florida East",
        32059: "NAD83 / Florida West",
        32060: "NAD83 / Florida North",
    }
    
    extended_mappings.update(state_plane_mappings)
    
    # Add common international systems
    international_mappings = {
        # European systems
        25832: "ETRS89 / UTM zone 32N",
        25833: "ETRS89 / UTM zone 33N", 
        25834: "ETRS89 / UTM zone 34N",
        25835: "ETRS89 / UTM zone 35N",
        
        # British National Grid
        27700: "OSGB 1936 / British National Grid",
        
        # Australian systems
        28348: "GDA94 / MGA zone 48",
        28349: "GDA94 / MGA zone 49",
        28350: "GDA94 / MGA zone 50",
        28351: "GDA94 / MGA zone 51",
        28352: "GDA94 / MGA zone 52",
        28353: "GDA94 / MGA zone 53",
        28354: "GDA94 / MGA zone 54",
        28355: "GDA94 / MGA zone 55",
        28356: "GDA94 / MGA zone 56",
        
        # Canadian systems
        3978: "NAD83 / Canada Atlas LCC",
        3979: "NAD83 / Canada Atlas LCC",
        
        # Polar systems
        3031: "WGS 84 / Antarctic Polar Stereographic",
        3413: "WGS 84 / NSIDC Sea Ice Polar Stereographic North",
        3976: "WGS 84 / NSIDC EASE-Grid 2.0 Global",
    }
    
    extended_mappings.update(international_mappings)
    
    logger.info(f"Generated {len(extended_mappings)} CRS mappings")
    return extended_mappings


def create_crs_dataframe(crs_mappings: Dict[int, str]) -> pd.DataFrame:
    """
    Create pandas DataFrame from CRS mappings.
    
    Args:
        crs_mappings: Dictionary of EPSG code to CRS name
        
    Returns:
        DataFrame with epsg_code and crs_name columns
    """
    data = [
        {"epsg_code": code, "crs_name": name}
        for code, name in sorted(crs_mappings.items())
    ]
    
    df = pd.DataFrame(data)
    
    # Add metadata columns
    df["category"] = df["crs_name"].apply(categorize_crs)
    df["is_projected"] = df["epsg_code"] > 4000  # Simple heuristic
    
    logger.info(f"Created DataFrame with {len(df)} CRS records")
    return df


def categorize_crs(crs_name: str) -> str:
    """Categorize CRS based on name patterns."""
    name_lower = crs_name.lower()
    
    if "utm" in name_lower:
        return "UTM"
    elif "state plane" in name_lower or "california" in name_lower or "texas" in name_lower:
        return "State Plane"
    elif "british" in name_lower or "osgb" in name_lower:
        return "British"
    elif "australia" in name_lower or "gda" in name_lower or "mga" in name_lower:
        return "Australian"
    elif "polar" in name_lower or "antarctic" in name_lower:
        return "Polar"
    elif "mercator" in name_lower:
        return "Mercator"
    elif any(geo in name_lower for geo in ["wgs 84", "nad83", "nad27", "etrs89"]):
        return "Geographic"
    else:
        return "Other"


def create_delta_table(df: pd.DataFrame, output_path: str) -> None:
    """
    Create Delta Lake table from DataFrame.
    
    Args:
        df: DataFrame with CRS mappings
        output_path: Path to write Delta table
    """
    if not DELTA_AVAILABLE:
        raise ImportError("deltalake package required")
    
    logger.info(f"Writing Delta table to: {output_path}")
    
    # Write Delta table
    write_deltalake(
        table_or_uri=output_path,
        data=df,
        mode="overwrite",
        partition_by=["category"],  # Partition by CRS category for efficient queries
        description="CRS lookup table for converting EPSG codes to human-readable names",
        configuration={
            "delta.autoOptimize.optimizeWrite": "true",
            "delta.autoOptimize.autoCompact": "true",
        }
    )
    
    logger.info(f"✅ Created Delta table with {len(df)} CRS mappings")
    
    # Print summary
    print(f"\n📊 CRS Lookup Table Summary:")
    print(f"   Total CRS codes: {len(df)}")
    print(f"   Categories:")
    for category, count in df["category"].value_counts().items():
        print(f"      {category}: {count}")
    
    print(f"\n📍 Table location: {output_path}")
    print(f"   Partitioned by: category")
    print(f"   Schema: epsg_code (int), crs_name (string), category (string), is_projected (bool)")


def main():
    """Main function to create CRS lookup table."""
    parser = argparse.ArgumentParser(description="Create CRS lookup table in Delta Lake")
    parser.add_argument(
        "--output-path", 
        required=True,
        help="Output path for Delta table (local or S3)"
    )
    parser.add_argument(
        "--include-extended",
        action="store_true",
        default=True,
        help="Include extended CRS mappings (default: True)"
    )
    
    args = parser.parse_args()
    
    if not DELTA_AVAILABLE:
        print("❌ deltalake package not available")
        print("Install with: uv add deltalake")
        return 1
    
    try:
        # Generate CRS mappings
        if args.include_extended:
            crs_mappings = get_extended_crs_mappings()
        else:
            crs_mappings = COMMON_CRS_MAPPING
        
        # Create DataFrame
        df = create_crs_dataframe(crs_mappings)
        
        # Create Delta table
        create_delta_table(df, args.output_path)
        
        print(f"\n🎯 Usage in code:")
        print(f"   from data_marketplace.cog.lookup_table_manager import get_lookup_manager")
        print(f"   manager = get_lookup_manager('{args.output_path}')")
        print(f"   crs_name = await manager.get_crs_name(32612)")
        
        return 0
        
    except Exception as e:
        logger.error(f"Failed to create CRS lookup table: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
