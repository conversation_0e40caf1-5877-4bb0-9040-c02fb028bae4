# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Delta Lake table cleanup and maintenance script.

Provides comprehensive table maintenance operations:
- Deduplicate rows based on scene_id + cog_key (removes duplicates)
- Vacuum old files with configurable retention
- Optimize small files with configurable target size
- Z-order optimization for query performance
- Table statistics and health checks
- Dry-run mode for safe testing

Usage:
    # Basic cleanup with default settings
    uv run scripts/clean_delta_table.py s3://bucket/table

    # Full cleanup (recommended): deduplicate + optimize + vacuum
    uv run scripts/clean_delta_table.py s3://bucket/table --full-cleanup

    # Deduplicate only
    uv run scripts/clean_delta_table.py s3://bucket/table --deduplicate

    # Custom vacuum retention (30 minutes for aggressive cleanup)
    uv run scripts/clean_delta_table.py s3://bucket/table --vacuum-hours 0.5

    # Optimize with custom target file size (256MB)
    uv run scripts/clean_delta_table.py s3://bucket/table --optimize --target-file-size 268435456

    # Z-order optimization for spatial queries
    uv run scripts/clean_delta_table.py s3://bucket/table --optimize --zorder-columns "year,month,collection"

    # Dry run to see what would be cleaned
    uv run scripts/clean_delta_table.py s3://bucket/table --full-cleanup --dry-run

    # Your specific use case: deduplicate, optimize, vacuum with 30min retention
    uv run scripts/clean_delta_table.py s3://bucket/table --deduplicate --optimize --vacuum-hours 0.5
"""

import argparse
import logging
import math
import sys
import time
from typing import Optional, List

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def setup_storage_options(table_path: str) -> dict:
    """Setup storage options for S3 or local filesystem."""
    storage_options = {}
    
    if table_path.startswith("s3://"):
        # Auto-detect region from bucket
        try:
            import subprocess
            bucket = table_path.split("/")[2]
            result = subprocess.run(
                ["aws", "s3api", "get-bucket-location", "--bucket", bucket],
                capture_output=True, text=True, timeout=10
            )
            if result.returncode == 0:
                import json
                location = json.loads(result.stdout).get("LocationConstraint")
                region = location if location else "us-east-1"
                storage_options["AWS_REGION"] = region
                logger.info(f"Detected S3 bucket '{bucket}' in region: {region}")
            else:
                storage_options["AWS_REGION"] = "us-west-2"  # Default
                logger.warning(f"Could not detect bucket region, using default: us-west-2")
        except Exception as e:
            storage_options["AWS_REGION"] = "us-west-2"  # Default
            logger.warning(f"Error detecting bucket region: {e}, using default: us-west-2")
        
        logger.info("Using IAM role or AWS CLI profile for S3 access")
    
    return storage_options


def get_table_stats(table_path: str, storage_options: dict) -> dict:
    """Get comprehensive table statistics."""
    try:
        from deltalake import DeltaTable
        
        dt = DeltaTable(table_path, storage_options=storage_options)
        
        # Basic stats - use file_uris for modern delta compatibility
        try:
            files = dt.file_uris()
            total_files = len(files)
        except AttributeError:
            # Fallback to deprecated files() method
            files = dt.files()
            total_files = len(files)

        if total_files == 0:
            return {"total_files": 0, "total_size_mb": 0, "avg_file_size_mb": 0}

        # Calculate file sizes - handle both string URIs and dict objects
        total_size = 0
        if files and isinstance(files[0], dict):
            # files() returns dict objects with size info
            total_size = sum(f.get("size", 0) for f in files)
        else:
            # file_uris() returns string URIs - estimate size as unavailable
            total_size = 0

        total_size_mb = total_size / (1024 * 1024) if total_size > 0 else 0
        avg_file_size_mb = total_size_mb / total_files if total_files > 0 and total_size > 0 else 0
        
        # Get row count using DuckDB
        try:
            import duckdb
            conn = duckdb.connect()
            conn.execute("INSTALL delta")
            conn.execute("LOAD delta")

            # Configure DuckDB for 7GB RAM environment
            conn.execute("SET memory_limit='5.5GB'")
            conn.execute("SET threads=2")
            conn.execute("SET preserve_insertion_order=false")
            
            if table_path.startswith("s3://") and storage_options:
                if "AWS_ACCESS_KEY_ID" in storage_options:
                    conn.execute(f"""
                        CREATE SECRET s3_secret (
                            TYPE S3,
                            KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                            SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                            REGION '{storage_options.get("AWS_REGION", "us-west-2")}'
                        )
                    """)
                else:
                    conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
            
            query = f"SELECT COUNT(*) FROM delta_scan('{table_path}')"
            result = conn.execute(query).fetchone()
            total_rows = int(result[0]) if result and result[0] is not None else 0
            conn.close()
        except Exception as e:
            logger.warning(f"Could not get row count: {e}")
            total_rows = None
        
        return {
            "total_files": total_files,
            "total_size_mb": round(total_size_mb, 2),
            "avg_file_size_mb": round(avg_file_size_mb, 2),
            "total_rows": total_rows,
            "version": dt.version(),
        }
        
    except Exception as e:
        logger.error(f"Error getting table stats: {e}")
        return {}


def vacuum_table(table_path: str, storage_options: dict, retention_hours: float, dry_run: bool = False) -> dict:
    """Vacuum old files from Delta table."""
    try:
        from deltalake import DeltaTable
        
        logger.info(f"🧹 {'[DRY RUN] ' if dry_run else ''}Vacuuming table with {retention_hours}h retention...")
        
        dt = DeltaTable(table_path, storage_options=storage_options)
        
        if dry_run:
            # Get list of files that would be deleted
            try:
                files_to_delete = dt.get_add_actions(flatten=True).to_pandas()
                logger.info(f"[DRY RUN] Would vacuum {len(files_to_delete)} old files")
                return {"files_deleted": len(files_to_delete), "dry_run": True}
            except Exception as e:
                logger.info(f"[DRY RUN] Would run vacuum with {retention_hours}h retention")
                return {"files_deleted": "unknown", "dry_run": True}
        else:
            # Actual vacuum - convert float hours to integer
            result = dt.vacuum(retention_hours=int(retention_hours), dry_run=False, enforce_retention_duration=False)
            files_deleted = len(result) if result else 0
            logger.info(f"✅ Vacuumed {files_deleted} old files")
            return {"files_deleted": files_deleted, "dry_run": False}
            
    except Exception as e:
        logger.error(f"Error during vacuum: {e}")
        return {"error": str(e)}


def deduplicate_table(table_path: str, storage_options: dict, dry_run: bool = False) -> dict:
    """Deduplicate Delta table using proper MERGE operations with streaming Arrow batches."""
    try:
        from deltalake import DeltaTable
        import pyarrow as pa
        import pyarrow.dataset as ds

        logger.info(f"🔄 {'[DRY RUN] ' if dry_run else ''}Deduplicating table using Delta Lake MERGE...")

        dt = DeltaTable(table_path, storage_options=storage_options)

        if dry_run:
            # Use PyArrow Dataset to count duplicates efficiently without loading all data
            dataset = dt.to_pyarrow_dataset()

            # Count total rows
            total_rows = dataset.count_rows()

            # Count unique combinations of scene_id + cog_key using streaming batches
            unique_combinations = set()
            duplicate_count = 0

            batch_iter = dataset.to_batches(columns=["scene_id", "cog_key"], batch_size=10000)
            for batch in batch_iter:
                scene_ids = batch.column("scene_id").to_pylist()
                cog_keys = batch.column("cog_key").to_pylist()

                for scene_id, cog_key in zip(scene_ids, cog_keys):
                    key = (scene_id, cog_key)
                    if key in unique_combinations:
                        duplicate_count += 1
                    else:
                        unique_combinations.add(key)

            logger.info(f"[DRY RUN] Found {duplicate_count} duplicate rows out of {total_rows} total")
            return {"duplicates_found": duplicate_count, "total_rows": total_rows, "dry_run": True}

        else:
            # Use Delta Lake MERGE for proper streaming deduplication
            # This approach doesn't load the entire table into memory

            # Step 1: Create a temporary table with only the latest records per scene_id + cog_key
            # We'll use DuckDB to create the deduplicated source data efficiently
            import duckdb

            conn = duckdb.connect()
            conn.execute("INSTALL delta")
            conn.execute("LOAD delta")

            # Configure DuckDB for 7GB RAM environment
            # Leave 1GB for OS/other processes, use 5.5GB for DuckDB
            conn.execute("SET memory_limit='5.5GB'")
            conn.execute("SET threads=2")  # Reduce threads to save memory
            conn.execute("SET preserve_insertion_order=false")  # Disable to save memory

            # Configure DuckDB for low-memory environment (7GB RAM total)
            # Reserve 1GB for OS and other processes, use max 5GB for DuckDB
            conn.execute("SET memory_limit='5GB'")
            conn.execute("SET threads=2")  # Reduce threads to save memory
            conn.execute("SET preserve_insertion_order=false")  # Save memory on sorting

            if table_path.startswith("s3://") and storage_options:
                if "AWS_ACCESS_KEY_ID" in storage_options:
                    conn.execute(f"""
                        CREATE SECRET s3_secret (
                            TYPE S3,
                            KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                            SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                            REGION '{storage_options.get("AWS_REGION", "us-west-2")}'
                        )
                    """)
                else:
                    conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")

            # Get counts first
            total_before = conn.execute(f"SELECT COUNT(*) FROM delta_scan('{table_path}')").fetchone()[0]
            logger.info(f"📊 Total rows before deduplication: {total_before:,}")

            # Count duplicates without loading data into memory
            duplicate_count_query = f"""
                SELECT COUNT(*) - COUNT(DISTINCT scene_id || '|' || cog_key) as duplicates
                FROM delta_scan('{table_path}')
            """
            duplicates_to_remove = conn.execute(duplicate_count_query).fetchone()[0]
            total_after = total_before - duplicates_to_remove

            logger.info(f"📊 Duplicates to remove: {duplicates_to_remove:,}")
            logger.info(f"📊 Rows after deduplication: {total_after:,}")

            conn.close()

            if duplicates_to_remove > 0:
                logger.info(f"🔄 Replacing table with {total_after} deduplicated rows (removing {duplicates_to_remove} duplicates)...")

                # Use chunked writes to avoid memory/timeout issues with large datasets
                # Following proven patterns from streaming processor (4000 rows per chunk)
                from deltalake.writer import write_deltalake

                chunk_size = 5000  # Proven optimal from streaming processor
                total_rows = len(deduplicated_arrow)
                total_chunks = math.ceil(total_rows / chunk_size)

                logger.info(f"📦 Writing {total_rows} rows in {total_chunks} chunks of {chunk_size} rows each...")

                for i in range(0, total_rows, chunk_size):
                    chunk_end = min(i + chunk_size, total_rows)
                    chunk = deduplicated_arrow.slice(i, chunk_end - i)
                    chunk_num = (i // chunk_size) + 1

                    # First chunk overwrites table, subsequent chunks append
                    mode = "overwrite" if i == 0 else "append"

                    logger.info(f"📝 Writing chunk {chunk_num}/{total_chunks} ({len(chunk)} rows, mode={mode})...")

                    write_deltalake(
                        table_path,
                        chunk,
                        mode=mode,
                        storage_options=storage_options,
                        partition_by=["year", "month"]  # Maintain partitioning
                        # Note: engine parameter removed in Delta Lake 1.0.0 - now defaults to Rust engine
                    )

                    logger.info(f"✅ Chunk {chunk_num}/{total_chunks} written successfully")

                logger.info(f"✅ Deduplication complete: removed {duplicates_to_remove} duplicates")
            else:
                logger.info("✅ No duplicates found - table is already clean")

            return {
                "duplicates_removed": duplicates_to_remove,
                "rows_before": total_before,
                "rows_after": total_after,
                "dry_run": False
            }

    except Exception as e:
        logger.error(f"Error during deduplication: {e}")
        return {"error": str(e)}


def optimize_table(table_path: str, storage_options: dict, target_size: Optional[int] = None,
                  zorder_columns: Optional[List[str]] = None, dry_run: bool = False) -> dict:
    """Optimize Delta table by compacting small files."""
    try:
        from deltalake import DeltaTable
        
        logger.info(f"🔧 {'[DRY RUN] ' if dry_run else ''}Optimizing table...")
        
        dt = DeltaTable(table_path, storage_options=storage_options)
        
        # Get current file stats - handle both file_uris and files methods
        try:
            files = dt.file_uris()
            # file_uris returns string URIs, can't get size info easily
            small_files = []  # Assume optimization needed if using file_uris
            total_files = len(files)
        except AttributeError:
            # Fallback to deprecated files() method which has size info
            files = dt.files()
            if files and isinstance(files[0], dict):
                small_files = [f for f in files if f.get("size", 0) < (target_size or 134217728)]  # Default 128MB
            else:
                small_files = []
            total_files = len(files)
        
        if dry_run:
            logger.info(f"[DRY RUN] Would optimize {len(small_files)} small files out of {total_files} total")
            if zorder_columns:
                logger.info(f"[DRY RUN] Would apply Z-order on columns: {zorder_columns}")
            return {"small_files": len(small_files), "total_files": total_files, "dry_run": True}
        
        # Actual optimization
        optimize_result = dt.optimize.compact(max_concurrent_tasks=2)
        
        # Z-order if specified
        if zorder_columns:
            logger.info(f"📊 Applying Z-order optimization on columns: {zorder_columns}")
            zorder_result = dt.optimize.z_order(zorder_columns,max_concurrent_tasks=2)
            logger.info(f"✅ Z-order optimization complete")
        
        logger.info(f"✅ Optimization complete: {len(small_files)} small files → optimized")
        return {
            "small_files_before": len(small_files),
            "total_files_before": total_files,
            "dry_run": False,
            "zorder_applied": bool(zorder_columns)
        }
        
    except Exception as e:
        logger.error(f"Error during optimization: {e}")
        return {"error": str(e)}


def main():
    parser = argparse.ArgumentParser(
        description="Delta Lake table cleanup and maintenance",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog=__doc__
    )
    
    parser.add_argument("table_path", help="Path to Delta Lake table (local or s3://)")
    parser.add_argument("--vacuum-hours", type=float, default=168,
                       help="Vacuum retention time in hours (default: 168 = 7 days, use 0.5 for 30 minutes)")
    parser.add_argument("--deduplicate", action="store_true",
                       help="Remove duplicate rows based on scene_id + cog_key")
    parser.add_argument("--optimize", action="store_true",
                       help="Run optimize (compact small files)")
    parser.add_argument("--target-file-size", type=int, default=134217728,
                       help="Target file size for optimization in bytes (default: 128MB)")
    parser.add_argument("--zorder-columns", type=str,
                       help="Comma-separated list of columns for Z-order optimization")
    parser.add_argument("--full-cleanup", action="store_true",
                       help="Run full cleanup: deduplicate + optimize + vacuum (recommended)")
    parser.add_argument("--dry-run", action="store_true",
                       help="Show what would be done without making changes")
    parser.add_argument("--stats-only", action="store_true",
                       help="Only show table statistics, no cleanup")
    
    args = parser.parse_args()
    
    # Setup storage options
    storage_options = setup_storage_options(args.table_path)
    
    # Parse Z-order columns
    zorder_columns = None
    if args.zorder_columns:
        zorder_columns = [col.strip() for col in args.zorder_columns.split(",")]
    
    logger.info("🚀 Starting Delta Lake table maintenance")
    logger.info(f"📍 Table: {args.table_path}")
    logger.info(f"🔑 Storage options: {list(storage_options.keys())}")
    
    # Get initial stats
    logger.info("📊 Getting table statistics...")
    initial_stats = get_table_stats(args.table_path, storage_options)
    
    if initial_stats:
        logger.info(f"📈 Table stats:")
        logger.info(f"   Files: {initial_stats.get('total_files', 'unknown')}")
        logger.info(f"   Size: {initial_stats.get('total_size_mb', 'unknown')} MB")
        logger.info(f"   Avg file size: {initial_stats.get('avg_file_size_mb', 'unknown')} MB")
        logger.info(f"   Rows: {initial_stats.get('total_rows', 'unknown')}")
        logger.info(f"   Version: {initial_stats.get('version', 'unknown')}")
    
    if args.stats_only:
        logger.info("✅ Stats-only mode complete")
        return
    
    start_time = time.time()

    # Handle full cleanup mode
    if args.full_cleanup:
        logger.info("🚀 Running FULL CLEANUP: deduplicate → optimize → vacuum")
        args.deduplicate = True
        args.optimize = True

    # Step 1: Deduplication (if requested)
    if args.deduplicate:
        logger.info("🔄 Step 1: Deduplicating table...")
        try:
            dedup_result = deduplicate_table(args.table_path, storage_options, args.dry_run)
            if "error" not in dedup_result:
                if args.dry_run:
                    logger.info(f"[DRY RUN] Would remove {dedup_result.get('duplicates_found', 0)} duplicates")
                else:
                    logger.info(f"✅ Removed {dedup_result.get('duplicates_removed', 0)} duplicate rows")
            else:
                logger.error(f"❌ Deduplication failed: {dedup_result.get('error', 'Unknown error')}")
        except Exception as e:
            logger.error(f"❌ Deduplication failed with exception: {e}")
            logger.info("🔄 Continuing with optimization and vacuum...")

    # Step 2: Optimize operation (if requested)
    if args.optimize:
        logger.info("🔧 Step 2: Optimizing table (compacting small files)...")
        optimize_result = optimize_table(
            args.table_path, storage_options, args.target_file_size, zorder_columns, args.dry_run
        )
        if "error" not in optimize_result and not args.dry_run:
            logger.info("✅ Optimization complete")

    # Step 3: Vacuum operation (always run unless stats-only)
    logger.info(f"🧹 Step 3: Vacuuming table (retention: {args.vacuum_hours}h)...")
    vacuum_result = vacuum_table(args.table_path, storage_options, args.vacuum_hours, args.dry_run)
    if "error" not in vacuum_result:
        if args.dry_run:
            logger.info(f"[DRY RUN] Would vacuum {vacuum_result.get('files_deleted', 'unknown')} old files")
        else:
            logger.info(f"✅ Vacuumed {vacuum_result.get('files_deleted', 0)} old files")
    
    # Final stats
    if not args.dry_run:
        logger.info("📊 Getting final statistics...")
        final_stats = get_table_stats(args.table_path, storage_options)
        
        if initial_stats and final_stats:
            size_saved = initial_stats.get('total_size_mb', 0) - final_stats.get('total_size_mb', 0)
            files_reduced = initial_stats.get('total_files', 0) - final_stats.get('total_files', 0)
            
            logger.info(f"💾 Space saved: {size_saved:.2f} MB")
            logger.info(f"📁 Files reduced: {files_reduced}")
    
    duration = time.time() - start_time
    logger.info(f"✅ {'[DRY RUN] ' if args.dry_run else ''}Maintenance complete in {duration:.2f}s")


if __name__ == "__main__":
    main()
