[ ] NAME:Current Task List DESCRIPTION:Root task for conversation __NEW_AGENT__
-[x] NAME:Analyze datetime semantics in unified_stac_table DESCRIPTION:Understand the different datetime fields and their meanings: STAC datetime vs COG capture time vs Delta Lake timestamps. Identify which datetime represents the actual satellite image capture time.
-[x] NAME:Examine table schema and datetime columns DESCRIPTION:Investigate all datetime-related columns in the table: datetime, created, updated, day fields. Understand their data types, formats, and what each represents.
-[x] NAME:Validate datetime data integrity DESCRIPTION:Sample data to verify datetime consistency, check for nulls, timezone issues, and ensure satellite capture time is correctly stored and accessible.
-[ ] NAME:Design correct partitioning strategy DESCRIPTION:Determine the optimal partitioning approach based on satellite capture datetime. Consider query patterns, data distribution, and performance implications.
-[ ] NAME:Plan datetime field corrections if needed DESCRIPTION:If datetime issues are found, design a strategy to fix them during restructuring without requiring full re-ingestion. Leverage existing STAC metadata.
-[ ] NAME:Update restructuring scripts with correct datetime logic DESCRIPTION:Modify scripts to handle datetime semantics correctly, ensuring satellite capture time is used for partitioning and preserved accurately.
-[ ] NAME:Create comprehensive validation for datetime correctness DESCRIPTION:Develop validation scripts to verify datetime accuracy before and after restructuring, including timezone, format, and semantic correctness checks.
-[x] NAME:Analyze current parquet optimization settings vs query patterns DESCRIPTION:Review current 32MB row group size vs 128MB target file size. Analyze query patterns (metadata counts, small range reads) and file distribution (76 files/month, 2-3 files/day, 80MB each) to optimize for slow networks and small reads.