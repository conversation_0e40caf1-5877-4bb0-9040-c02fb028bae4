# SPDX-FileCopyrightText: Terrafloww Labs, 2025
# Example environment configuration for data marketplace

# S3 Configuration
S3_BUCKET_NAME=your-data-bucket
S3_REGION=us-west-2
S3_ACCESS_KEY_ID=your-access-key
S3_SECRET_ACCESS_KEY=your-secret-key
# S3_ENDPOINT_URL=http://localhost:9000  # For MinIO or custom S3

# Delta Lake Configuration  
DELTA_TABLE_ROOT=delta-tables
DELTA_MASTER_INDEX_TABLE=master_index
DELTA_VACUUM_RETENTION_HOURS=168

# Spatial Configuration
SPATIAL_S2_CELL_LEVEL=6
SPATIAL_BBOX_PRECISION=6

# Parquet Configuration - Optimized for STAC metadata
PARQUET_ROW_GROUP_SIZE=104857600  # 100MB (optimal for ~100K STAC records)
PARQUET_COMPRESSION=snappy  # Better for dictionary data + fast reads
PARQUET_USE_DICTIONARY=true  # Enable dictionary encoding for strings
PARQUET_ENABLE_BLOOM_FILTERS=true

# API Configuration
API_HOST=0.0.0.0
API_PORT=8000
API_WORKERS=1
API_MAX_ITEMS_PER_REQUEST=10000
API_ENABLE_CORS=true

# General Configuration
ENVIRONMENT=development
DEBUG=false
LOG_LEVEL=INFO
