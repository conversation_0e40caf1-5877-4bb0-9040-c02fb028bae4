# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
S3 smoke test for streaming processor with real Delta Lake writes.

This test validates the complete streaming ingestion pipeline:
- Real STAC API data fetching
- Streaming processor with modular components
- Real S3 Delta Lake table creation
- Data validation and read-back verification
"""

import asyncio
import logging
import os
from typing import Dict

import pytest
from deltalake import DeltaTable

from data_marketplace.ingestion.delta_stac_ingester import <PERSON><PERSON>tac<PERSON>ngester

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


def get_test_s3_config() -> Dict[str, str]:
    """Get S3 configuration for testing."""
    # Check for required environment variables
    aws_access_key = os.getenv("AWS_ACCESS_KEY_ID")
    aws_secret_key = os.getenv("AWS_SECRET_ACCESS_KEY")
    aws_region = os.getenv("AWS_REGION", "us-west-2")
    test_bucket = os.getenv("TEST_S3_BUCKET")

    if not aws_access_key or not aws_secret_key:
        pytest.skip(
            "AWS credentials not available (AWS_ACCESS_KEY_ID, AWS_SECRET_ACCESS_KEY)"
        )

    if not test_bucket:
        pytest.skip("TEST_S3_BUCKET environment variable not set")

    return {
        "AWS_ACCESS_KEY_ID": aws_access_key,
        "AWS_SECRET_ACCESS_KEY": aws_secret_key,
        "AWS_REGION": aws_region,
        "test_bucket": test_bucket,
    }


@pytest.mark.asyncio
@pytest.mark.integration
async def test_s3_streaming_smoke():
    """
    Comprehensive S3 smoke test for streaming processor.

    This test:
    1. Creates a real S3 Delta Lake table
    2. Ingests a small amount of real STAC data using streaming processor
    3. Validates the table was created correctly
    4. Reads back data to verify correctness
    5. Cleans up test resources
    """
    s3_config = get_test_s3_config()

    # Create unique test table path
    import uuid

    test_id = str(uuid.uuid4())[:8]
    table_path = f"s3://{s3_config['test_bucket']}/test-streaming-smoke-{test_id}"

    logger.info("🧪 Starting S3 streaming smoke test")
    logger.info(f"📍 Test table: {table_path}")

    try:
        # Initialize ingester with S3 storage
        storage_options = {
            "AWS_ACCESS_KEY_ID": s3_config["AWS_ACCESS_KEY_ID"],
            "AWS_SECRET_ACCESS_KEY": s3_config["AWS_SECRET_ACCESS_KEY"],
            "AWS_REGION": s3_config["AWS_REGION"],
        }

        ingester = DeltaStacIngester(
            unified_table_path=table_path,
            storage_options=storage_options,
            stac_api_url="https://earth-search.aws.element84.com/v1",
        )

        # Test with a small area and recent date for fast execution
        bbox = [2.0, 46.0, 3.0, 47.0]  # Small area in France
        datetime_range = "2024-01-01/2024-01-02"  # Just 2 days
        max_items = 5  # Very small test

        logger.info(
            f"🚀 Starting ingestion: {max_items} items, bbox={bbox}, date={datetime_range}"
        )

        # Run ingestion
        results = await ingester.ingest_stac_collection(
            stac_api_url="https://earth-search.aws.element84.com/v1",
            collection_id="sentinel-2-l2a",
            max_items=max_items,
            datetime_range=datetime_range,
            bbox=bbox,
            batch_size=10,  # Small batches for testing
            max_concurrent_cog_requests=5,  # Conservative for testing
            max_concurrent_stac_items=2,
        )

        # Validate ingestion results
        logger.info(f"📊 Ingestion results: {results}")

        assert results["stac_items_processed"] > 0, "No STAC items were processed"
        assert results["unified_records_written"] > 0, "No unified records were written"
        assert (
            len(results["errors"]) == 0
        ), f"Errors occurred during ingestion: {results['errors']}"

        # Validate performance metrics
        perf = results["performance"]
        assert perf["items_per_second"] > 0, "Items per second should be positive"
        assert (
            perf["unified_records_per_second"] > 0
        ), "Records per second should be positive"

        logger.info(
            f"✅ Ingestion completed: {results['stac_items_processed']} items → {results['unified_records_written']} records"
        )
        logger.info(
            f"⚡ Performance: {perf['items_per_second']:.2f} items/sec, {perf['unified_records_per_second']:.2f} records/sec"
        )

        # Validate Delta Lake table was created
        logger.info(f"🔍 Validating Delta Lake table at {table_path}")

        delta_table = DeltaTable(table_path, storage_options=storage_options)

        # Check table properties
        assert delta_table.version() >= 0, "Delta table should have a valid version"
        files = delta_table.files()
        assert len(files) > 0, "Delta table should have data files"

        logger.info(
            f"📁 Delta table: version {delta_table.version()}, {len(files)} files"
        )

        # Read back data to validate structure
        df = delta_table.to_pandas()

        assert (
            len(df) == results["unified_records_written"]
        ), "Record count mismatch between ingestion and table"

        # Validate required columns exist
        required_columns = [
            "scene_id",
            "cog_key",
            "cog_href",
            "cog_width",
            "cog_height",
            "year",
            "month",
            "collection_id",
        ]

        for col in required_columns:
            assert col in df.columns, f"Required column '{col}' missing from table"

        # Validate data types and values
        assert df["scene_id"].notna().all(), "scene_id should not have null values"
        assert df["cog_key"].notna().all(), "cog_key should not have null values"
        assert df["cog_href"].notna().all(), "cog_href should not have null values"
        assert (df["cog_width"] > 0).all(), "cog_width should be positive"
        assert (df["cog_height"] > 0).all(), "cog_height should be positive"

        logger.info(
            f"✅ Data validation passed: {len(df)} records with correct structure"
        )
        logger.info(f"📋 Sample record keys: {list(df['cog_key'].unique()[:5])}")

        # Test table info functionality
        table_info = ingester.get_table_info()
        assert table_info["unified_table"][
            "exists"
        ], "Table info should show table exists"
        assert (
            table_info["unified_table"]["version"] >= 0
        ), "Table info should show valid version"

        logger.info("🎉 S3 streaming smoke test PASSED!")
        logger.info(
            f"📈 Final stats: {results['stac_items_processed']} items → {results['unified_records_written']} records in {results['duration_seconds']:.1f}s"
        )

        return {
            "table_path": table_path,
            "records_written": results["unified_records_written"],
            "items_processed": results["stac_items_processed"],
            "duration_seconds": results["duration_seconds"],
            "performance": results["performance"],
        }

    except Exception as e:
        logger.error(f"❌ S3 streaming smoke test FAILED: {e}")
        raise

    finally:
        # Cleanup: Delete test table
        try:
            logger.info(f"🧹 Cleaning up test table: {table_path}")
            # Note: In a real test, you might want to keep the table for debugging
            # For now, we'll just log the cleanup intent
            logger.info(f"ℹ️ Test table left for manual cleanup: {table_path}")
        except Exception as cleanup_error:
            logger.warning(f"⚠️ Cleanup failed: {cleanup_error}")


if __name__ == "__main__":
    # Allow running the test directly
    asyncio.run(test_s3_streaming_smoke())
