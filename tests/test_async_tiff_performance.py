#!/usr/bin/env python3
"""
Performance comparison test between our current COG parser and async-tiff.

This script tests both parsers on the same COG URLs to compare:
1. Parsing speed
2. Metadata extraction accuracy
3. Memory usage
4. Error rates
"""

import asyncio
import time
import logging
from typing import List, Dict, Any

# Our current parser
from data_marketplace.cog.cog_parser import COGHeaderParser
from data_marketplace.cog.stac_cog_processor import StacCogProcessor

# New async-tiff parser
try:
    from data_marketplace.cog.async_tiff_parser import AsyncTiffCogParser, AsyncTiffStacCogProcessor
    ASYNC_TIFF_AVAILABLE = True
except ImportError as e:
    print(f"async-tiff not available: {e}")
    ASYNC_TIFF_AVAILABLE = False

logging.basicConfig(level=logging.DEBUG)
logger = logging.getLogger(__name__)

# Test URLs - mix of S3 and HTTPS COGs
TEST_URLS_SMALL = [
    # Sentinel-2 COGs (public, no auth required)
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/12/S/UF/2022/6/S2B_12SUF_20220609_0_L2A/B04.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/12/S/UF/2022/6/S2B_12SUF_20220609_0_L2A/B03.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/12/S/UF/2022/6/S2B_12SUF_20220609_0_L2A/B02.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/12/S/UF/2022/6/S2B_12SUF_20220609_0_L2A/B08.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/12/S/UF/2022/6/S2B_12SUF_20220609_0_L2A/B11.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/V/EG/2025/8/S2B_43VEG_20250812_0_L2A/AOT.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/V/EG/2025/8/S2B_43VEG_20250812_0_L2A/B01.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/V/EG/2025/8/S2B_43VEG_20250812_0_L2A/B02.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/V/EG/2025/8/S2B_43VEG_20250812_0_L2A/B03.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B02.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B03.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B04.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B08.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B11.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B12.tif",
    "https://sentinel-cogs.s3.us-west-2.amazonaws.com/sentinel-s2-l2a-cogs/43/L/EK/2025/8/S2B_43LEK_20250812_0_L2A/B8A.tif",
]

# Larger test set for better performance measurement
TEST_URLS_LARGE = TEST_URLS_SMALL


async def test_current_parser(urls: List[str]) -> Dict[str, Any]:
    """Test our current aiohttp-based parser."""
    logger.info("🔄 Testing current COG parser...")
    
    start_time = time.time()
    successful_parses = 0
    failed_parses = 0
    results = []
    
    async with COGHeaderParser(max_concurrent=10) as parser:
        for url in urls:
            try:
                result = await parser.parse_cog_header(url)
                if result:
                    successful_parses += 1
                    results.append({
                        "url": url,
                        "width": result.width,
                        "height": result.height,
                        "tile_width": result.tile_width,
                        "tile_height": result.tile_height,
                        "dtype": result.dtype,  # Old parser has string dtype
                        "compression": result.compression,  # Old parser has int compression
                        "crs": result.crs,  # Old parser has int crs
                    })
                else:
                    failed_parses += 1
                    logger.warning(f"Failed to parse: {url}")
            except Exception as e:
                failed_parses += 1
                logger.error(f"Error parsing {url}: {e}")
    
    end_time = time.time()
    duration = end_time - start_time
    
    return {
        "parser": "current",
        "duration": duration,
        "successful_parses": successful_parses,
        "failed_parses": failed_parses,
        "urls_per_second": len(urls) / duration,
        "results": results,
    }


async def test_async_tiff_parser(urls: List[str]) -> Dict[str, Any]:
    """Test async-tiff based parser with optimized batch processing."""
    if not ASYNC_TIFF_AVAILABLE:
        return {
            "parser": "async-tiff",
            "error": "async-tiff not available",
        }

    logger.info("🚀 Testing async-tiff parser...")

    start_time = time.time()
    successful_parses = 0
    failed_parses = 0
    results = []

    parser = AsyncTiffCogParser()

    # Use batch processing for better performance (like tiff-dumper)
    batch_results = await parser.parse_batch(urls)

    for i, result in enumerate(batch_results):
        url = urls[i]
        if result and not isinstance(result, Exception):
            successful_parses += 1
            results.append({
                "url": url,
                "width": result.width,
                "height": result.height,
                "tile_width": result.tile_width,
                "tile_height": result.tile_height,
                "dtype_code": result.dtype_code,  # New parser has int dtype_code
                "compression_code": result.compression_code,  # New parser has int compression_code
                "crs_code": result.crs_code,  # New parser has int crs_code
            })
        else:
            failed_parses += 1
            if isinstance(result, Exception):
                logger.error(f"Error parsing {url}: {result}")
            else:
                logger.warning(f"Failed to parse: {url}")

    end_time = time.time()
    duration = end_time - start_time

    return {
        "parser": "async-tiff",
        "duration": duration,
        "successful_parses": successful_parses,
        "failed_parses": failed_parses,
        "urls_per_second": len(urls) / duration,
        "results": results,
    }


async def test_stac_processor_compatibility():
    """Test that async-tiff processor is compatible with STAC processor interface."""
    if not ASYNC_TIFF_AVAILABLE:
        logger.warning("Skipping STAC processor test - async-tiff not available")
        return
    
    logger.info("🔧 Testing STAC processor compatibility...")
    
    # Test single asset parsing (used by streaming consumer)
    processor = AsyncTiffStacCogProcessor()
    
    test_url = TEST_URLS_SMALL[0]
    asset_key = "B04"
    asset_data = {
        "type": "image/tiff; application=geotiff",
        "roles": ["data"],
        "title": "Band 4 - Red",
    }
    
    try:
        start_time = time.time()
        records = await processor.parse_cog_headers_for_asset(test_url, asset_key, asset_data)
        end_time = time.time()
        
        if records:
            record = records[0]
            logger.info(f"✅ STAC processor test successful in {end_time - start_time:.3f}s")
            logger.info(f"   Record keys: {list(record.keys())}")
            logger.info(f"   COG dimensions: {record.get('cog_width')}x{record.get('cog_height')}")
            logger.info(f"   Tile size: {record.get('cog_tile_width')}x{record.get('cog_tile_height')}")
            logger.info(f"   Data type: {record.get('cog_dtype')}")
            logger.info(f"   CRS: {record.get('cog_crs')}")
        else:
            logger.error("❌ STAC processor test failed - no records returned")
            
    except Exception as e:
        logger.error(f"❌ STAC processor test failed: {e}")


def print_comparison(current_result: Dict[str, Any], async_tiff_result: Dict[str, Any]):
    """Print detailed comparison of results."""
    print("\n" + "="*80)
    print("🏁 PERFORMANCE COMPARISON RESULTS")
    print("="*80)
    
    if "error" in async_tiff_result:
        print(f"❌ async-tiff test failed: {async_tiff_result['error']}")
        return
    
    # Performance comparison
    current_duration = current_result["duration"]
    async_tiff_duration = async_tiff_result["duration"]
    speedup = current_duration / async_tiff_duration if async_tiff_duration > 0 else 0
    
    print(f"📊 PERFORMANCE:")
    print(f"   Current parser:  {current_duration:.3f}s ({current_result['urls_per_second']:.2f} URLs/sec)")
    print(f"   async-tiff:      {async_tiff_duration:.3f}s ({async_tiff_result['urls_per_second']:.2f} URLs/sec)")
    print(f"   🚀 Speedup:      {speedup:.2f}x")
    
    # Success rate comparison
    print(f"\n📈 SUCCESS RATES:")
    print(f"   Current parser:  {current_result['successful_parses']}/{current_result['successful_parses'] + current_result['failed_parses']}")
    print(f"   async-tiff:      {async_tiff_result['successful_parses']}/{async_tiff_result['successful_parses'] + async_tiff_result['failed_parses']}")
    
    # Metadata comparison (first successful result from each)
    if current_result["results"] and async_tiff_result["results"]:
        print(f"\n🔍 METADATA COMPARISON (first result):")
        current_meta = current_result["results"][0]
        async_tiff_meta = async_tiff_result["results"][0]
        
        # Map old field names to new field names for comparison
        field_mapping = {
            "width": "width",
            "height": "height",
            "tile_width": "tile_width",
            "tile_height": "tile_height",
            "dtype": "dtype_code",
            "compression": "compression_code",
            "crs": "crs_code"
        }

        for old_key, new_key in field_mapping.items():
            current_val = current_meta.get(old_key)
            async_tiff_val = async_tiff_meta.get(new_key)
            match = "✅" if current_val == async_tiff_val else "❌"
            print(f"   {old_key:12}: {current_val:15} | {async_tiff_val:15} {match}")


async def main():
    """Run performance comparison tests."""
    print("🧪 COG Parser Performance Comparison")
    print("="*50)

    # Test with larger dataset for better performance measurement
    test_urls = TEST_URLS_LARGE
    print(f"Testing {len(test_urls)} COG URLs...")

    # Test current parser
    current_result = await test_current_parser(test_urls)

    # Test async-tiff parser
    async_tiff_result = await test_async_tiff_parser(test_urls)
    
    # Test STAC processor compatibility
    await test_stac_processor_compatibility()
    
    # Print comparison
    print_comparison(current_result, async_tiff_result)
    
    print(f"\n💡 RECOMMENDATION:")
    if "error" not in async_tiff_result and async_tiff_result["urls_per_second"] > current_result["urls_per_second"]:
        speedup = async_tiff_result["urls_per_second"] / current_result["urls_per_second"]
        print(f"   ✅ async-tiff is {speedup:.2f}x faster - RECOMMENDED for production use")
        print(f"   🔧 Integration: Replace StacCogProcessor with AsyncTiffStacCogProcessor")
    else:
        print(f"   ⚠️  Current parser performs better or async-tiff has issues")
        print(f"   🔧 Recommendation: Stick with current implementation")


if __name__ == "__main__":
    asyncio.run(main())
