# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Local smoke test for modular streaming processor.

This test validates the refactored streaming processor components:
- StreamingProducer
- StreamingConsumer  
- StreamingCollector
- StreamingStacProcessor orchestration
"""

import asyncio
import logging
import tempfile
from pathlib import Path

import pytest

from data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester
from data_marketplace.ingestion.streaming_processor import StreamingStacProcessor
from data_marketplace.ingestion.stac_schema import UnifiedStacSchema


# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class MockStacItem:
    """Mock STAC item for testing."""
    
    def __init__(self, item_id: str, num_cog_assets: int = 3):
        self.id = item_id
        self.collection_id = "test-collection"
        self.assets = {}
        
        # Create mock COG assets
        for i in range(num_cog_assets):
            asset_key = f"band_{i+1}"
            self.assets[asset_key] = MockAsset(
                href=f"https://example.com/{item_id}/{asset_key}.tif",
                media_type="image/tiff; profile=cloud-optimized"
            )
    
    def to_dict(self):
        return {
            "id": self.id,
            "collection": self.collection_id,
            "type": "Feature",
            "geometry": {
                "type": "Polygon",
                "coordinates": [[[2.0, 46.0], [3.0, 46.0], [3.0, 47.0], [2.0, 47.0], [2.0, 46.0]]]
            },
            "properties": {
                "datetime": "2024-01-01T12:00:00Z",
                "platform": "test-platform",
                "instruments": ["test-instrument"],
            },
            "assets": {k: v.to_dict() for k, v in self.assets.items()},
        }


class MockAsset:
    """Mock asset for testing."""
    
    def __init__(self, href: str, media_type: str):
        self.href = href
        self.media_type = media_type
        self.roles = ["data"]
        self.title = "Test Asset"
    
    def to_dict(self):
        return {
            "href": self.href,
            "type": self.media_type,
            "roles": self.roles,
            "title": self.title,
        }


class MockCogProcessor:
    """Mock COG processor for testing."""
    
    def is_cog_asset(self, asset) -> bool:
        """Check if asset is a COG."""
        return hasattr(asset, 'media_type') and 'cloud-optimized' in asset.media_type
    
    async def parse_cog_headers_for_item_fast(self, stac_item, max_concurrent_requests: int, only_keys=None):
        """Mock fast COG header parsing."""
        cog_records = []
        
        for asset_key, asset in stac_item.assets.items():
            if only_keys and asset_key not in only_keys:
                continue
                
            if self.is_cog_asset(asset):
                cog_records.append({
                    "asset_key": asset_key,
                    "asset_href": asset.href,
                    "asset_title": asset.title,
                    "asset_roles": asset.roles,
                    "cog_width": 1024,
                    "cog_height": 1024,
                    "cog_tile_width": 512,
                    "cog_tile_height": 512,
                    "cog_dtype": "uint16",
                    "cog_compression": "lzw",
                    "cog_crs": "EPSG:4326",
                })
        
        return cog_records

    async def parse_cog_headers_for_asset(self, asset_href: str, asset_key: str, asset_data: dict):
        """Mock COG header parsing for a single asset."""
        return [{
            "cog_key": asset_key,
            "cog_href": asset_href,
            "cog_title": asset_data.get("title"),
            "cog_roles": asset_data.get("roles", []),
            "cog_width": 1024,
            "cog_height": 1024,
            "cog_tile_width": 512,
            "cog_tile_height": 512,
            "cog_dtype": "uint16",
            "cog_compression": "lzw",
            "cog_crs": "EPSG:4326",
        }]


@pytest.mark.asyncio
async def test_modular_streaming_components():
    """Test the modular streaming processor components."""
    
    logger.info("🧪 Testing modular streaming processor components")
    
    # Create test data
    stac_items = [
        MockStacItem("item_001", num_cog_assets=3),
        MockStacItem("item_002", num_cog_assets=2), 
        MockStacItem("item_003", num_cog_assets=4),
    ]
    
    expected_records = sum(len(item.assets) for item in stac_items)
    logger.info(f"📊 Test data: {len(stac_items)} items, {expected_records} expected records")
    
    # Initialize components
    schema = UnifiedStacSchema()
    cog_processor = MockCogProcessor()
    
    processor = StreamingStacProcessor(
        unified_schema=schema,
        cog_processor=cog_processor,
        existing_key_checker=None,  # No deduplication for this test
    )
    
    logger.info(f"🔧 Processor config: {processor.n_consumers} consumers, buffer size {processor.max_buffer_size}")
    
    # Process items and collect results
    total_records = 0
    batch_count = 0
    
    async for batch_stats, unified_records in processor.process_stac_items_in_batches(
        iter(stac_items),  # Regular iterator
        batch_size=5,
        max_concurrent_cog_requests=10,
        max_concurrent_stac_items=3
    ):
        batch_count += 1
        records_in_batch = len(unified_records)
        total_records += records_in_batch
        
        logger.info(f"📦 Batch {batch_count}: {records_in_batch} records")
        
        # Validate batch statistics
        assert batch_stats["unified_records_written"] == records_in_batch
        assert batch_stats["cog_assets_processed"] == records_in_batch
        
        # Validate record structure
        for record in unified_records:
            assert "scene_id" in record
            assert "cog_key" in record
            assert "cog_href" in record
            assert "cog_width" in record
            assert "cog_height" in record
            assert record["cog_width"] == 1024
            assert record["cog_height"] == 1024
    
    # Validate final results
    assert total_records == expected_records, f"Expected {expected_records} records, got {total_records}"
    assert batch_count > 0, "Should have processed at least one batch"
    
    # Validate statistics
    stats = processor.stats
    assert stats.items_processed == len(stac_items)
    assert stats.records_created == expected_records
    assert stats.errors == 0
    
    logger.info("✅ Modular streaming test PASSED!")
    logger.info(f"📈 Final stats: {stats.items_processed} items → {stats.records_created} records")
    logger.info(f"⚡ Performance: {stats.items_per_second:.2f} items/sec, {stats.records_per_second:.2f} records/sec")


@pytest.mark.asyncio
async def test_local_delta_ingestion():
    """Test complete ingestion pipeline with local Delta Lake table."""
    
    logger.info("🧪 Testing complete ingestion pipeline locally")
    
    with tempfile.TemporaryDirectory() as temp_dir:
        table_path = Path(temp_dir) / "test_unified_table"
        
        # Initialize ingester
        ingester = DeltaStacIngester(
            unified_table_path=str(table_path),
            storage_options={},  # Local filesystem
        )
        
        # Create mock processor that returns our test data
        class MockStreamingProcessor:
            def __init__(self):
                self.stac_api_url = None
                self.existing_key_checker = None
            
            def clear_existing_keys_cache(self):
                pass
            
            async def process_stac_items_in_batches(self, stac_items, batch_size, max_concurrent_cog_requests, max_concurrent_stac_items):
                # Create test records
                test_records = []
                for i in range(3):
                    for j in range(2):  # 2 COG assets per item
                        test_records.append({
                            "scene_id": f"test_item_{i}",
                            "cog_key": f"band_{j+1}",
                            "cog_href": f"https://example.com/test_item_{i}/band_{j+1}.tif",
                            "cog_width": 1024,
                            "cog_height": 1024,
                            "cog_tile_width": 512,
                            "cog_tile_height": 512,
                            "cog_dtype": "uint16",
                            "collection_id": "test-collection",
                            "year": 2024,
                            "month": 1,
                            "datetime": "2024-01-01T12:00:00Z",
                        })
                
                # Yield in batches
                for i in range(0, len(test_records), batch_size):
                    batch = test_records[i:i + batch_size]
                    batch_stats = {
                        "stac_items_processed": len(batch),
                        "cog_assets_processed": len(batch),
                        "unified_records_written": len(batch),
                        "errors": [],
                    }
                    yield batch_stats, batch
        
        # Replace processor for testing
        ingester.stac_processor = MockStreamingProcessor()
        
        # Run a mock ingestion
        results = await ingester.ingest_stac_collection(
            stac_api_url="https://example.com/stac",
            collection_id="test-collection",
            max_items=3,
            batch_size=3,
        )
        
        # Validate results
        assert results["stac_items_processed"] > 0
        assert results["unified_records_written"] > 0
        assert len(results["errors"]) == 0
        
        # Validate table was created
        table_info = ingester.get_table_info()
        assert table_info["unified_table"]["exists"]
        assert table_info["unified_table"]["version"] >= 0
        
        logger.info("✅ Local Delta ingestion test PASSED!")
        logger.info(f"📈 Results: {results['unified_records_written']} records written")


if __name__ == "__main__":
    # Allow running tests directly
    asyncio.run(test_modular_streaming_components())
    asyncio.run(test_local_delta_ingestion())
