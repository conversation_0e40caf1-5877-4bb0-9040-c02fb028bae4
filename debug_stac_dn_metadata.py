#!/usr/bin/env python3
"""
Debug STAC metadata to understand why DN scale/offset are NULL.

This script fetches the same STAC items we ingested and examines:
1. Raw STAC item properties
2. Asset-level metadata
3. raster:bands extension
4. Any scale/offset values present
"""

import asyncio
import json
import logging
from typing import Dict, Any, List

try:
    import pystac_client
    import duckdb
    MODULES_AVAILABLE = True
except ImportError as e:
    print(f"Required modules not available: {e}")
    MODULES_AVAILABLE = False

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# Same parameters as our ingestion
STAC_API_URL = "https://earth-search.aws.element84.com/v1"
COLLECTION = "sentinel-2-l2a"
DATETIME_RANGE = "2025-02-01/2025-02-02"
MAX_ITEMS = 3

# Table path to compare with
TABLE_PATH = "s3://tf-datalake-bucket/deltalake-tables/unified_stac_async_tiff_test"


def setup_duckdb_connection():
    """Set up DuckDB connection to query our Delta table."""
    conn = duckdb.connect()
    conn.execute("INSTALL delta")
    conn.execute("LOAD delta")
    conn.execute("INSTALL httpfs")
    conn.execute("LOAD httpfs")
    conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain, REGION 'us-west-2')")
    return conn


def get_delta_table_cog_keys(conn):
    """Get COG keys from our Delta table for comparison."""
    try:
        query = f"""
        SELECT DISTINCT cog_key, cog_dn_scale, cog_dn_offset
        FROM delta_scan('{TABLE_PATH}')
        ORDER BY cog_key
        """
        result = conn.execute(query).fetchall()
        
        delta_data = {}
        for row in result:
            cog_key, dn_scale, dn_offset = row
            delta_data[cog_key] = {"dn_scale": dn_scale, "dn_offset": dn_offset}
        
        return delta_data
    except Exception as e:
        print(f"❌ Error querying Delta table: {e}")
        return {}


async def fetch_stac_items():
    """Fetch the same STAC items we ingested."""
    if not MODULES_AVAILABLE:
        return []
    
    try:
        print(f"🔍 Fetching STAC items from {STAC_API_URL}")
        print(f"   Collection: {COLLECTION}")
        print(f"   Date range: {DATETIME_RANGE}")
        print(f"   Max items: {MAX_ITEMS}")
        
        # Connect to STAC API
        catalog = pystac_client.Client.open(STAC_API_URL)
        
        # Search for items
        search = catalog.search(
            collections=[COLLECTION],
            datetime=DATETIME_RANGE,
            max_items=MAX_ITEMS
        )
        
        items = list(search.items())
        print(f"✅ Found {len(items)} STAC items")
        return items
        
    except Exception as e:
        print(f"❌ Error fetching STAC items: {e}")
        return []


def analyze_stac_item_properties(item):
    """Analyze STAC item properties for scale/offset metadata."""
    print(f"\n📋 STAC Item: {item.id}")
    print("="*60)
    
    # Check item-level properties
    print("🔍 Item Properties:")
    properties = item.properties
    
    # Look for any scale/offset related properties
    scale_offset_props = {}
    for key, value in properties.items():
        if any(term in key.lower() for term in ['scale', 'offset', 'band', 'raster']):
            scale_offset_props[key] = value
    
    if scale_offset_props:
        print("   Scale/Offset related properties found:")
        for key, value in scale_offset_props.items():
            print(f"      {key}: {value}")
    else:
        print("   ❌ No scale/offset properties found at item level")
    
    # Check for raster extension
    if 'raster:bands' in properties:
        print(f"   ✅ raster:bands extension found:")
        raster_bands = properties['raster:bands']
        for i, band in enumerate(raster_bands):
            print(f"      Band {i}: {band}")
    else:
        print("   ❌ No raster:bands extension found")
    
    return scale_offset_props


def analyze_asset_metadata(item, delta_data):
    """Analyze asset-level metadata for scale/offset."""
    print(f"\n🔍 Asset Analysis for {item.id}")
    print("-"*50)
    
    asset_analysis = {}
    
    for asset_key, asset in item.assets.items():
        print(f"\n📁 Asset: {asset_key}")
        
        # Get asset metadata
        asset_dict = asset.to_dict()
        
        # Look for scale/offset in asset
        asset_scale_offset = {}
        for key, value in asset_dict.items():
            if any(term in key.lower() for term in ['scale', 'offset', 'band', 'raster']):
                asset_scale_offset[key] = value
        
        # Check raster:bands in asset
        raster_bands = asset_dict.get('raster:bands', [])
        
        print(f"   Asset properties: {len(asset_dict)} total")
        print(f"   Asset roles: {asset_dict.get('roles', [])}")
        print(f"   Asset type: {asset_dict.get('type', 'unknown')}")
        
        if asset_scale_offset:
            print(f"   ✅ Scale/offset properties:")
            for key, value in asset_scale_offset.items():
                print(f"      {key}: {value}")
        else:
            print(f"   ❌ No scale/offset properties")
        
        if raster_bands:
            print(f"   ✅ raster:bands ({len(raster_bands)} bands):")
            for i, band in enumerate(raster_bands):
                scale = band.get('scale')
                offset = band.get('offset')
                print(f"      Band {i}: scale={scale}, offset={offset}")
                if scale is not None or offset is not None:
                    asset_scale_offset[f'raster_band_{i}_scale'] = scale
                    asset_scale_offset[f'raster_band_{i}_offset'] = offset
        else:
            print(f"   ❌ No raster:bands")
        
        # Compare with Delta table data
        if asset_key in delta_data:
            delta_scale = delta_data[asset_key]['dn_scale']
            delta_offset = delta_data[asset_key]['dn_offset']
            print(f"   📊 Delta table values:")
            print(f"      dn_scale: {delta_scale}")
            print(f"      dn_offset: {delta_offset}")
            
            # Check if they match
            if raster_bands and len(raster_bands) > 0:
                stac_scale = raster_bands[0].get('scale')
                stac_offset = raster_bands[0].get('offset')
                
                if stac_scale == delta_scale and stac_offset == delta_offset:
                    print(f"      ✅ Values match STAC metadata")
                elif stac_scale is None and stac_offset is None and delta_scale is None and delta_offset is None:
                    print(f"      ✅ Both NULL (no metadata available)")
                else:
                    print(f"      ❌ Mismatch: STAC scale={stac_scale}, offset={stac_offset}")
        
        asset_analysis[asset_key] = {
            'asset_metadata': asset_scale_offset,
            'raster_bands': raster_bands,
            'delta_values': delta_data.get(asset_key, {})
        }
    
    return asset_analysis


def print_summary(all_analysis):
    """Print summary of findings."""
    print(f"\n🎯 SUMMARY OF FINDINGS")
    print("="*50)
    
    total_assets = 0
    assets_with_scale_offset = 0
    assets_with_raster_bands = 0
    
    for item_id, analysis in all_analysis.items():
        for asset_key, asset_data in analysis.items():
            total_assets += 1
            
            if asset_data['asset_metadata']:
                assets_with_scale_offset += 1
            
            if asset_data['raster_bands']:
                assets_with_raster_bands += 1
    
    print(f"📊 Statistics:")
    print(f"   Total assets analyzed: {total_assets}")
    print(f"   Assets with scale/offset: {assets_with_scale_offset} ({assets_with_scale_offset/total_assets*100:.1f}%)")
    print(f"   Assets with raster:bands: {assets_with_raster_bands} ({assets_with_raster_bands/total_assets*100:.1f}%)")
    
    print(f"\n💡 Findings:")
    if assets_with_scale_offset == 0:
        print("   ❌ NO assets have scale/offset metadata in STAC")
        print("   → This explains why we have NULLs in Delta table")
        print("   → Sentinel-2 L2A may not include radiometric calibration in STAC metadata")
    else:
        print(f"   ✅ {assets_with_scale_offset} assets have scale/offset metadata")
        print("   → Check if our parser is extracting them correctly")
    
    if assets_with_raster_bands == 0:
        print("   ❌ NO assets have raster:bands extension")
        print("   → This is the primary source for DN scale/offset")
    else:
        print(f"   ✅ {assets_with_raster_bands} assets have raster:bands extension")


async def main():
    """Main function to debug STAC metadata."""
    print("🔍 STAC Metadata Debug Analysis")
    print("="*60)
    print("Investigating why DN scale/offset are NULL in Delta table")
    
    if not MODULES_AVAILABLE:
        print("❌ Required modules not available")
        return 1
    
    try:
        # Get Delta table data for comparison
        conn = setup_duckdb_connection()
        delta_data = get_delta_table_cog_keys(conn)
        print(f"📊 Found {len(delta_data)} COG keys in Delta table")
        
        # Fetch STAC items
        items = await fetch_stac_items()
        if not items:
            print("❌ No STAC items found")
            return 1
        
        # Analyze each item
        all_analysis = {}
        for item in items:
            # Analyze item properties
            item_props = analyze_stac_item_properties(item)
            
            # Analyze asset metadata
            asset_analysis = analyze_asset_metadata(item, delta_data)
            
            all_analysis[item.id] = asset_analysis
        
        # Print summary
        print_summary(all_analysis)
        
        conn.close()
        return 0
        
    except Exception as e:
        print(f"❌ Error in analysis: {e}")
        return 1


if __name__ == "__main__":
    exit(asyncio.run(main()))
