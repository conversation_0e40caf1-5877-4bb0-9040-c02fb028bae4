# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""S2 spatial indexing utilities for efficient spatial queries."""

import logging
from typing import List, Tuple, Optional, Dict, Any
from shapely.geometry import Polygon, box
import s2sphere
import pyarrow as pa

logger = logging.getLogger(__name__)


class S2Utils:
    """Utilities for S2 spatial indexing and cell operations."""

    def __init__(self, cell_level: int = 6, adaptive_levels: bool = True):
        """
        Initialize S2 utilities.

        Args:
            cell_level: Default S2 cell level for spatial indexing (0-30)
            adaptive_levels: If True, automatically adjust cell level based on geometry complexity
        """
        if not 0 <= cell_level <= 30:
            raise ValueError("S2 cell level must be between 0 and 30")

        self.cell_level = cell_level
        self.adaptive_levels = adaptive_levels
        self.logger = logger

    def polygon_to_s2_cells(
        self, polygon: Polygon, level: Optional[int] = None, max_cells: int = 100
    ) -> List[int]:
        """
        Convert a polygon to covering S2 cell IDs using enhanced S2 covering.

        Args:
            polygon: Shapely polygon
            level: S2 cell level (uses adaptive selection if None)
            max_cells: Maximum number of cells to return

        Returns:
            List of S2 cell IDs as integers, sorted by Hilbert curve order
        """
        if level is None:
            if self.adaptive_levels:
                level = self._select_optimal_cell_level(polygon, max_cells)
            else:
                level = self.cell_level

        try:
            # Convert Shapely polygon to S2 region
            s2_region = self._shapely_to_s2_region(polygon)

            # Create S2 region coverer with enhanced settings
            coverer = s2sphere.RegionCoverer()

            # Use level range for better coverage
            min_level = max(0, level - 1)
            max_level = min(30, level + 1)

            coverer.min_level = min_level
            coverer.max_level = max_level
            coverer.max_cells = max_cells

            # Set level mod for better cell distribution
            coverer.level_mod = 1

            # Get covering cells
            covering = coverer.get_covering(s2_region)
            cell_ids = []
            for cell in covering:
                cell_id = cell.id()
                # Validate S2 cell ID is within UInt64 range
                if isinstance(cell_id, int) and 0 <= cell_id <= (2**64 - 1):
                    cell_ids.append(cell_id)
                else:
                    self.logger.warning(f"Invalid S2 cell ID in covering: {cell_id}")

            # Enhanced Hilbert curve ordering
            cell_ids = self.sort_cells_by_hilbert_order(cell_ids)

            self.logger.debug(
                f"Polygon covered by {len(cell_ids)} S2 cells "
                f"(levels {min_level}-{max_level}, target: {level})"
            )
            return cell_ids

        except Exception as e:
            self.logger.warning(f"Failed to create S2 covering for polygon: {e}")
            # Fallback to bbox corner sampling
            return self._polygon_to_s2_cells_fallback(polygon, level)

    def _shapely_to_s2_region(self, polygon: Polygon) -> s2sphere.LatLngRect:
        """
        Convert Shapely polygon to S2 region using enhanced bounding box approach.

        Since s2sphere doesn't have polygon support, we use an enhanced
        bounding box approach with better coverage strategies.
        """
        try:
            # Use enhanced bounding box approach
            return self._create_enhanced_s2_region(polygon)
        except Exception as e:
            self.logger.debug(
                f"Failed to create enhanced S2 region, using simple bbox: {e}"
            )
            # Fallback to simple bounding box approach
            return self._create_s2_bbox_region(polygon)

    def _create_enhanced_s2_region(self, polygon: Polygon) -> s2sphere.LatLngRect:
        """
        Create enhanced S2 region that better represents polygon coverage.

        This method analyzes the polygon shape and creates a more representative
        bounding region than simple min/max bounds.
        """
        # Get polygon bounds
        minx, miny, maxx, maxy = polygon.bounds

        # For better coverage, slightly expand the bounds based on polygon complexity
        num_vertices = len(polygon.exterior.coords)

        # Calculate expansion factor based on complexity
        if num_vertices > 50:
            expansion_factor = 0.01  # 1% expansion for complex polygons
        elif num_vertices > 20:
            expansion_factor = 0.005  # 0.5% expansion for medium polygons
        else:
            expansion_factor = 0.002  # 0.2% expansion for simple polygons

        # Calculate expansion amounts
        width = maxx - minx
        height = maxy - miny

        expand_x = width * expansion_factor
        expand_y = height * expansion_factor

        # Apply expansion
        expanded_minx = minx - expand_x
        expanded_miny = miny - expand_y
        expanded_maxx = maxx + expand_x
        expanded_maxy = maxy + expand_y

        # Create S2 LatLng points
        sw = s2sphere.LatLng.from_degrees(expanded_miny, expanded_minx)
        ne = s2sphere.LatLng.from_degrees(expanded_maxy, expanded_maxx)

        # Create S2 LatLngRect
        return s2sphere.LatLngRect.from_point_pair(sw, ne)

    def _create_s2_bbox_region(self, polygon: Polygon) -> s2sphere.LatLngRect:
        """Create S2 LatLngRect from polygon bounding box."""
        # Get polygon bounds
        minx, miny, maxx, maxy = polygon.bounds

        # Create S2 LatLng points
        sw = s2sphere.LatLng.from_degrees(miny, minx)
        ne = s2sphere.LatLng.from_degrees(maxy, maxx)

        # Create S2 LatLngRect (this is a valid S2 region)
        return s2sphere.LatLngRect.from_point_pair(sw, ne)

    def _select_optimal_cell_level(self, polygon: Polygon, max_cells: int = 100) -> int:
        """
        Select optimal S2 cell level based on polygon complexity and size.

        Args:
            polygon: Shapely polygon to analyze
            max_cells: Maximum number of cells desired

        Returns:
            Optimal S2 cell level (0-30)
        """
        try:
            # Calculate polygon area (rough approximation)
            bounds = polygon.bounds
            minx, miny, maxx, maxy = bounds
            area_degrees = (maxx - minx) * (maxy - miny)

            # Calculate polygon complexity (number of vertices)
            num_vertices = len(polygon.exterior.coords)

            # Base level selection on area
            if area_degrees > 100:  # Very large area (> 100 deg²)
                base_level = 3
            elif area_degrees > 10:  # Large area (> 10 deg²)
                base_level = 4
            elif area_degrees > 1:  # Medium area (> 1 deg²)
                base_level = 5
            elif area_degrees > 0.1:  # Small area (> 0.1 deg²)
                base_level = 6
            elif area_degrees > 0.01:  # Very small area (> 0.01 deg²)
                base_level = 7
            else:  # Tiny area
                base_level = 8

            # Adjust based on complexity
            if num_vertices > 100:
                complexity_adjustment = 2
            elif num_vertices > 50:
                complexity_adjustment = 1
            elif num_vertices > 20:
                complexity_adjustment = 0
            else:
                complexity_adjustment = -1

            # Calculate final level
            optimal_level = base_level + complexity_adjustment

            # Clamp to valid range
            optimal_level = max(0, min(30, optimal_level))

            self.logger.debug(
                f"Selected S2 level {optimal_level} for polygon "
                f"(area: {area_degrees:.6f} deg², vertices: {num_vertices})"
            )

            return optimal_level

        except Exception as e:
            self.logger.warning(
                f"Failed to select optimal cell level: {e}, using default"
            )
            return self.cell_level

    def _polygon_to_s2_cells_fallback(self, polygon: Polygon, level: int) -> List[int]:
        """Fallback method using bbox corner sampling."""
        bounds = polygon.bounds
        minx, miny, maxx, maxy = bounds

        # Get cells for corners and center
        cells = set()
        points = [
            (minx, miny),  # bottom-left
            (maxx, miny),  # bottom-right
            (maxx, maxy),  # top-right
            (minx, maxy),  # top-left
            ((minx + maxx) / 2, (miny + maxy) / 2),  # center
        ]

        for lon, lat in points:
            cell_id = self.point_to_s2_cell(lon, lat, level)
            cells.add(cell_id)

        cell_ids = list(cells)
        cell_ids.sort()  # Sort for consistency
        return cell_ids

    def bbox_to_s2_cells(
        self,
        bbox: Tuple[float, float, float, float],
        level: Optional[int] = None,
        max_cells: int = 100,
    ) -> List[int]:
        """
        Convert a bounding box to covering S2 cell IDs with enhanced covering.

        Args:
            bbox: Bounding box as (minx, miny, maxx, maxy)
            level: S2 cell level (uses instance level if None)
            max_cells: Maximum number of cells to return

        Returns:
            List of S2 cell IDs as integers, sorted by Hilbert curve order
        """
        polygon = box(*bbox)
        return self.polygon_to_s2_cells(polygon, level, max_cells)

    def point_to_s2_cell(
        self, lon: float, lat: float, level: Optional[int] = None
    ) -> int:
        """
        Convert a point to S2 cell ID.

        Args:
            lon: Longitude
            lat: Latitude
            level: S2 cell level (uses instance level if None)

        Returns:
            S2 cell ID as integer
        """
        if level is None:
            level = self.cell_level

        point = s2sphere.LatLng.from_degrees(lat, lon)
        cell = s2sphere.CellId.from_lat_lng(point).parent(level)
        cell_id = cell.id()

        # Ensure the cell ID is a valid UInt64 value
        if not isinstance(cell_id, int) or cell_id < 0 or cell_id > (2**64 - 1):
            self.logger.warning(
                f"Invalid S2 cell ID generated: {cell_id} for point ({lon}, {lat})"
            )
            return 0  # Return a safe default

        return cell_id

    def s2_cell_to_polygon(self, cell_id: int) -> Polygon:
        """
        Convert S2 cell ID to shapely polygon.

        Args:
            cell_id: S2 cell ID as integer

        Returns:
            Shapely polygon representing the cell
        """
        cell = s2sphere.CellId(cell_id)
        s2_cell = s2sphere.Cell(cell)

        # Get cell vertices
        vertices = []
        for i in range(4):
            vertex = s2_cell.get_vertex(i)
            lat_lng = s2sphere.LatLng.from_point(vertex)
            vertices.append((lat_lng.lng().degrees, lat_lng.lat().degrees))

        # Close the polygon
        vertices.append(vertices[0])

        return Polygon(vertices)

    def get_s2_cell_token(self, cell_id: int) -> str:
        """
        Get S2 cell token string representation.

        Args:
            cell_id: S2 cell ID as integer

        Returns:
            S2 cell token string
        """
        cell = s2sphere.CellId(cell_id)
        return cell.to_token()

    def s2_cell_from_token(self, token: str) -> int:
        """
        Get S2 cell ID from token string.

        Args:
            token: S2 cell token string

        Returns:
            S2 cell ID as integer
        """
        cell = s2sphere.CellId.from_token(token)
        return cell.id()

    def get_parent_cells(self, cell_id: int, parent_level: int) -> int:
        """
        Get parent cell at specified level.

        Args:
            cell_id: S2 cell ID
            parent_level: Target parent level

        Returns:
            Parent cell ID
        """
        cell = s2sphere.CellId(cell_id)
        parent = cell.parent(parent_level)
        return parent.id()

    def get_child_cells(self, cell_id: int, child_level: int) -> List[int]:
        """
        Get all child cells at specified level.

        Args:
            cell_id: S2 cell ID
            child_level: Target child level

        Returns:
            List of child cell IDs
        """
        cell = s2sphere.CellId(cell_id)
        current_level = cell.level()

        if child_level <= current_level:
            raise ValueError("Child level must be greater than current level")

        # Get all children at target level
        children = []
        level_diff = child_level - current_level

        def get_children_recursive(current_cell, remaining_levels):
            if remaining_levels == 0:
                children.append(current_cell.id())
                return

            for i in range(4):
                child = current_cell.child(i)
                get_children_recursive(child, remaining_levels - 1)

        get_children_recursive(cell, level_diff)
        return children

    def sort_cells_by_hilbert_order(self, cell_ids: List[int]) -> List[int]:
        """
        Sort S2 cell IDs by Hilbert curve order for spatial locality.

        Enhanced implementation that considers cell level and spatial proximity.

        Args:
            cell_ids: List of S2 cell IDs

        Returns:
            Sorted list of S2 cell IDs optimized for spatial locality
        """
        if not cell_ids:
            return []

        try:
            # Group cells by level for better ordering
            cells_by_level = {}
            for cell_id in cell_ids:
                cell = s2sphere.CellId(cell_id)
                level = cell.level()
                if level not in cells_by_level:
                    cells_by_level[level] = []
                cells_by_level[level].append(cell_id)

            # Sort each level separately and combine
            sorted_cells = []
            for level in sorted(cells_by_level.keys()):
                level_cells = cells_by_level[level]
                # S2 cell IDs are ordered by Hilbert curve when sorted numerically
                level_cells.sort()
                sorted_cells.extend(level_cells)

            self.logger.debug(
                f"Sorted {len(cell_ids)} cells by Hilbert order across {len(cells_by_level)} levels"
            )
            return sorted_cells

        except Exception as e:
            self.logger.warning(
                f"Failed to sort by Hilbert order: {e}, using simple sort"
            )
            return sorted(cell_ids)

    def get_hilbert_range_for_bbox(
        self, bbox: Tuple[float, float, float, float], level: Optional[int] = None
    ) -> Tuple[int, int]:
        """
        Get the Hilbert curve range (min, max cell IDs) for a bounding box.

        Args:
            bbox: Bounding box as (minx, miny, maxx, maxy)
            level: S2 cell level (uses instance level if None)

        Returns:
            Tuple of (min_cell_id, max_cell_id) for range queries
        """
        if level is None:
            level = self.cell_level

        # Get corner cells
        minx, miny, maxx, maxy = bbox

        # Get cells for all corners
        corner_cells = []
        for lon, lat in [(minx, miny), (maxx, miny), (maxx, maxy), (minx, maxy)]:
            cell_id = self.point_to_s2_cell(lon, lat, level)
            corner_cells.append(cell_id)

        # Return the range
        return (min(corner_cells), max(corner_cells))

    def hilbert_sort_by_s2(
        self, records: List[Dict[str, Any]], s2_field: str = "s2_cell_id"
    ) -> List[Dict[str, Any]]:
        """
        Sort records by S2 cell ID using enhanced Hilbert curve ordering for optimal spatial locality.

        Args:
            records: List of record dictionaries
            s2_field: Field name containing S2 cell ID

        Returns:
            Sorted list of records optimized for spatial locality
        """
        try:
            if not records:
                return records

            # Extract S2 cell IDs for enhanced sorting
            cell_ids = [record.get(s2_field, 0) for record in records]

            # Get optimal ordering using enhanced Hilbert sort
            sorted_cell_ids = self.sort_cells_by_hilbert_order(cell_ids)

            # Create mapping from cell ID to records
            cell_to_records = {}
            for record in records:
                cell_id = record.get(s2_field, 0)
                if cell_id not in cell_to_records:
                    cell_to_records[cell_id] = []
                cell_to_records[cell_id].append(record)

            # Build sorted records list
            sorted_records = []
            for cell_id in sorted_cell_ids:
                if cell_id in cell_to_records:
                    sorted_records.extend(cell_to_records[cell_id])

            self.logger.debug(
                f"Sorted {len(records)} records by enhanced S2 Hilbert ordering"
            )
            return sorted_records

        except Exception as e:
            self.logger.warning(
                f"Failed to sort by S2 cell ID: {e}, returning original order"
            )
            return records

    @staticmethod
    def get_s2_schema_field(field_name: str = "s2_cell_id") -> pa.Field:
        """
        Get PyArrow schema field for S2 cell ID.

        Args:
            field_name: Name of the field

        Returns:
            PyArrow field for S2 cell ID (string for Delta Lake compatibility)
        """
        return pa.field(field_name, pa.string())

    @staticmethod
    def create_s2_array(cell_ids: List[int]) -> pa.Array:
        """
        Create PyArrow array from S2 cell IDs.

        Args:
            cell_ids: List of S2 cell IDs

        Returns:
            PyArrow array (string for Delta Lake compatibility)
        """
        # Convert integers to strings for Delta Lake compatibility
        string_cell_ids = [str(cell_id) for cell_id in cell_ids]
        return pa.array(string_cell_ids, type=pa.string())
