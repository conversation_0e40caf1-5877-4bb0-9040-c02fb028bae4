# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Bounding box utilities with 4-field struct support for optimal Parquet statistics."""

import logging
from typing import List, <PERSON><PERSON>, Union, Dict
from dataclasses import dataclass
import pyarrow as pa
from shapely.geometry import Polygon, box

logger = logging.getLogger(__name__)


@dataclass
class BboxStruct:
    """4-field bounding box structure for optimal Parquet statistics."""

    xmin: float
    ymin: float
    xmax: float
    ymax: float

    def to_tuple(self) -> <PERSON><PERSON>[float, float, float, float]:
        """Convert to tuple format (minx, miny, maxx, maxy)."""
        return (self.xmin, self.ymin, self.xmax, self.ymax)

    def to_dict(self) -> Dict[str, float]:
        """Convert to dictionary format."""
        return {
            "xmin": self.xmin,
            "ymin": self.ymin,
            "xmax": self.xmax,
            "ymax": self.ymax,
        }

    def to_polygon(self) -> Polygon:
        """Convert to shapely polygon."""
        return box(self.xmin, self.ymin, self.xmax, self.ymax)

    def intersects(self, other: "BboxStruct") -> bool:
        """Check if this bbox intersects with another."""
        return not (
            self.xmax < other.xmin
            or other.xmax < self.xmin
            or self.ymax < other.ymin
            or other.ymax < self.ymin
        )

    def contains(self, other: "BboxStruct") -> bool:
        """Check if this bbox contains another."""
        return (
            self.xmin <= other.xmin
            and self.ymin <= other.ymin
            and self.xmax >= other.xmax
            and self.ymax >= other.ymax
        )

    def area(self) -> float:
        """Calculate bbox area."""
        return (self.xmax - self.xmin) * (self.ymax - self.ymin)


class BboxUtils:
    """Utilities for bounding box operations and conversions."""

    def __init__(self, precision: int = 6):
        """
        Initialize bbox utilities.

        Args:
            precision: Decimal precision for coordinates
        """
        self.precision = precision
        self.logger = logger

    @staticmethod
    def get_bbox_schema() -> pa.StructType:
        """
        Get PyArrow schema for 4-field bbox struct.

        Returns:
            PyArrow struct type for bbox
        """
        return pa.struct(
            [
                ("xmin", pa.float64()),
                ("ymin", pa.float64()),
                ("xmax", pa.float64()),
                ("ymax", pa.float64()),
            ]
        )

    def tuple_to_bbox_struct(
        self, bbox_tuple: Tuple[float, float, float, float]
    ) -> BboxStruct:
        """
        Convert tuple to BboxStruct.

        Args:
            bbox_tuple: Bounding box as (minx, miny, maxx, maxy)

        Returns:
            BboxStruct instance
        """
        minx, miny, maxx, maxy = bbox_tuple
        return BboxStruct(
            xmin=round(minx, self.precision),
            ymin=round(miny, self.precision),
            xmax=round(maxx, self.precision),
            ymax=round(maxy, self.precision),
        )

    def list_to_bbox_struct(self, bbox_list: List[float]) -> BboxStruct:
        """
        Convert list to BboxStruct.

        Args:
            bbox_list: Bounding box as [minx, miny, maxx, maxy]

        Returns:
            BboxStruct instance
        """
        if len(bbox_list) != 4:
            raise ValueError("Bbox list must have exactly 4 elements")

        return self.tuple_to_bbox_struct(tuple(bbox_list))

    def polygon_to_bbox_struct(self, polygon: Polygon) -> BboxStruct:
        """
        Convert shapely polygon to BboxStruct.

        Args:
            polygon: Shapely polygon

        Returns:
            BboxStruct instance
        """
        bounds = polygon.bounds
        return self.tuple_to_bbox_struct(bounds)

    def stac_bbox_to_struct(self, stac_bbox: List[float]) -> BboxStruct:
        """
        Convert STAC bbox to BboxStruct.

        STAC bbox format: [west, south, east, north] or [minx, miny, maxx, maxy]

        Args:
            stac_bbox: STAC bbox as list

        Returns:
            BboxStruct instance
        """
        return self.list_to_bbox_struct(stac_bbox)

    def create_bbox_array(self, bbox_structs: List[BboxStruct]) -> pa.Array:
        """
        Create PyArrow array from list of BboxStruct.

        Args:
            bbox_structs: List of BboxStruct instances

        Returns:
            PyArrow struct array
        """
        data = [bbox.to_dict() for bbox in bbox_structs]
        return pa.array(data, type=self.get_bbox_schema())

    def expand_bbox(self, bbox: BboxStruct, buffer_degrees: float) -> BboxStruct:
        """
        Expand bbox by buffer distance.

        Args:
            bbox: Original bbox
            buffer_degrees: Buffer distance in degrees

        Returns:
            Expanded BboxStruct
        """
        return BboxStruct(
            xmin=bbox.xmin - buffer_degrees,
            ymin=bbox.ymin - buffer_degrees,
            xmax=bbox.xmax + buffer_degrees,
            ymax=bbox.ymax + buffer_degrees,
        )

    def union_bboxes(self, bboxes: List[BboxStruct]) -> BboxStruct:
        """
        Calculate union of multiple bboxes.

        Args:
            bboxes: List of BboxStruct instances

        Returns:
            Union BboxStruct
        """
        if not bboxes:
            raise ValueError("Cannot calculate union of empty bbox list")

        xmin = min(bbox.xmin for bbox in bboxes)
        ymin = min(bbox.ymin for bbox in bboxes)
        xmax = max(bbox.xmax for bbox in bboxes)
        ymax = max(bbox.ymax for bbox in bboxes)

        return BboxStruct(xmin=xmin, ymin=ymin, xmax=xmax, ymax=ymax)

    def intersection_bbox(
        self, bbox1: BboxStruct, bbox2: BboxStruct
    ) -> Union[BboxStruct, None]:
        """
        Calculate intersection of two bboxes.

        Args:
            bbox1: First bbox
            bbox2: Second bbox

        Returns:
            Intersection BboxStruct or None if no intersection
        """
        if not bbox1.intersects(bbox2):
            return None

        xmin = max(bbox1.xmin, bbox2.xmin)
        ymin = max(bbox1.ymin, bbox2.ymin)
        xmax = min(bbox1.xmax, bbox2.xmax)
        ymax = min(bbox1.ymax, bbox2.ymax)

        return BboxStruct(xmin=xmin, ymin=ymin, xmax=xmax, ymax=ymax)

    def validate_bbox(self, bbox: BboxStruct) -> bool:
        """
        Validate bbox coordinates.

        Args:
            bbox: BboxStruct to validate

        Returns:
            True if valid, False otherwise
        """
        # Check coordinate order
        if bbox.xmin >= bbox.xmax or bbox.ymin >= bbox.ymax:
            self.logger.warning(f"Invalid bbox coordinate order: {bbox}")
            return False

        # Check longitude bounds
        if bbox.xmin < -180 or bbox.xmax > 180:
            self.logger.warning(f"Longitude out of bounds: {bbox}")
            return False

        # Check latitude bounds
        if bbox.ymin < -90 or bbox.ymax > 90:
            self.logger.warning(f"Latitude out of bounds: {bbox}")
            return False

        return True

    def normalize_bbox(self, bbox: BboxStruct) -> BboxStruct:
        """
        Normalize bbox coordinates to valid ranges.

        Args:
            bbox: BboxStruct to normalize

        Returns:
            Normalized BboxStruct
        """
        # Clamp longitude to [-180, 180]
        xmin = max(-180, min(180, bbox.xmin))
        xmax = max(-180, min(180, bbox.xmax))

        # Clamp latitude to [-90, 90]
        ymin = max(-90, min(90, bbox.ymin))
        ymax = max(-90, min(90, bbox.ymax))

        # Ensure proper order
        if xmin > xmax:
            xmin, xmax = xmax, xmin
        if ymin > ymax:
            ymin, ymax = ymax, ymin

        return BboxStruct(xmin=xmin, ymin=ymin, xmax=xmax, ymax=ymax)
