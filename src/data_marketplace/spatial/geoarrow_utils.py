# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
GeoArrow utilities for optimal spatial data encoding in Parquet.
"""

import logging
from typing import Dict, Any, List, Optional
import pyarrow as pa
import geoarrow.pyarrow as ga
import geopandas as gpd
from shapely.geometry import shape
import json

logger = logging.getLogger(__name__)


class GeoArrowUtils:
    """Utilities for converting between GeoJSON, WKB, WKT and GeoArrow formats."""

    def __init__(self):
        self.logger = logger

    def geojson_to_geoarrow(self, geometries: List[Dict[str, Any]]) -> pa.Array:
        """
        Convert GeoJSON geometries to native GeoArrow format.

        Args:
            geometries: List of GeoJSON geometry dictionaries

        Returns:
            PyArrow array with native GeoArrow encoding (not WKB)
        """
        try:
            # Convert GeoJSON to Shapely geometries
            shapely_geoms = []
            for geom in geometries:
                if geom is None:
                    shapely_geoms.append(None)
                else:
                    shapely_geoms.append(shape(geom))

            # Create GeoSeries and convert to native GeoArrow
            gdf = gpd.GeoSeries(shapely_geoms)

            # Use as_geoarrow() for native encoding instead of WKB
            return ga.as_geoarrow(gdf)

        except Exception as e:
            self.logger.warning(f"Failed to convert GeoJSON to native GeoArrow: {e}")
            # Fallback to WKB encoding
            return self.geojson_to_wkb_array(geometries)

    def geojson_to_wkb_array(self, geometries: List[Dict[str, Any]]) -> pa.Array:
        """
        Convert GeoJSON geometries to WKB array with GeoArrow metadata.

        Args:
            geometries: List of GeoJSON geometry dictionaries

        Returns:
            PyArrow array with WKB data and GeoArrow metadata
        """
        wkb_data = []

        for geom in geometries:
            if geom is None:
                wkb_data.append(None)
            else:
                try:
                    # Convert GeoJSON to Shapely then to WKB
                    shapely_geom = shape(geom)
                    wkb_bytes = shapely_geom.wkb
                    wkb_data.append(wkb_bytes)
                except Exception as e:
                    self.logger.warning(f"Failed to convert geometry to WKB: {e}")
                    wkb_data.append(None)

        # Create WKB array with GeoArrow metadata
        return ga.as_wkb(wkb_data)

    def create_bbox_struct_array(self, bboxes: List[Optional[tuple]]) -> pa.Array:
        """
        Create 4-field bbox struct array for optimal Parquet statistics.

        Args:
            bboxes: List of bbox tuples (xmin, ymin, xmax, ymax) or None

        Returns:
            PyArrow struct array with bbox fields
        """
        bbox_data = []

        for bbox in bboxes:
            if bbox is None or len(bbox) != 4:
                bbox_data.append(
                    {"xmin": None, "ymin": None, "xmax": None, "ymax": None}
                )
            else:
                bbox_data.append(
                    {
                        "xmin": float(bbox[0]),
                        "ymin": float(bbox[1]),
                        "xmax": float(bbox[2]),
                        "ymax": float(bbox[3]),
                    }
                )

        # Create struct array
        bbox_struct_type = pa.struct(
            [
                ("xmin", pa.float64()),
                ("ymin", pa.float64()),
                ("xmax", pa.float64()),
                ("ymax", pa.float64()),
            ]
        )

        return pa.array(bbox_data, type=bbox_struct_type)

    def extract_bbox_from_geojson(self, geometry: Dict[str, Any]) -> Optional[tuple]:
        """
        Extract bbox from GeoJSON geometry.

        Args:
            geometry: GeoJSON geometry dictionary

        Returns:
            Bbox tuple (xmin, ymin, xmax, ymax) or None
        """
        try:
            shapely_geom = shape(geometry)
            bounds = shapely_geom.bounds
            return (bounds[0], bounds[1], bounds[2], bounds[3])
        except Exception as e:
            self.logger.warning(f"Failed to extract bbox from geometry: {e}")
            return None

    def validate_geometry(self, geometry: Dict[str, Any]) -> bool:
        """
        Validate GeoJSON geometry.

        Args:
            geometry: GeoJSON geometry dictionary

        Returns:
            True if valid, False otherwise
        """
        try:
            shapely_geom = shape(geometry)
            return shapely_geom.is_valid
        except Exception:
            return False

    def create_geoparquet_metadata(
        self,
        geometry_column: str = "geometry",
        crs: Optional[Dict[str, Any]] = None,
        geometries: Optional[List[Dict[str, Any]]] = None,
        encoding: str = "WKB",
    ) -> Dict[str, Any]:
        """
        Create GeoParquet metadata for the schema based on actual data.

        Args:
            geometry_column: Name of the geometry column
            crs: Coordinate reference system
            geometries: List of GeoJSON geometries to calculate bbox from
            encoding: Geometry encoding ("WKB", "point", "polygon", etc.)

        Returns:
            GeoParquet metadata dictionary following v1.1.0 specification
        """
        # Calculate actual bbox from geometries if provided
        if geometries:
            bbox = self._calculate_bbox_from_geometries(geometries)
        else:
            # No bbox if we don't have actual data
            bbox = None

        # Detect geometry types from actual data
        if geometries:
            geometry_types = self._detect_geometry_types(geometries)
        else:
            geometry_types = []  # Empty array when types are not known

        # Default to WGS84 PROJJSON if no CRS provided
        if crs is None:
            crs = {
                "$schema": "https://proj.org/schemas/v0.4/projjson.schema.json",
                "type": "GeographicCRS",
                "name": "WGS 84",
                "datum": {
                    "type": "GeodeticReferenceFrame",
                    "name": "World Geodetic System 1984",
                    "ellipsoid": {
                        "name": "WGS 84",
                        "semi_major_axis": 6378137,
                        "inverse_flattening": 298.257223563,
                    },
                },
                "coordinate_system": {
                    "subtype": "ellipsoidal",
                    "axis": [
                        {
                            "name": "Geodetic latitude",
                            "abbreviation": "Lat",
                            "direction": "north",
                            "unit": "degree",
                        },
                        {
                            "name": "Geodetic longitude",
                            "abbreviation": "Lon",
                            "direction": "east",
                            "unit": "degree",
                        },
                    ],
                },
                "id": {"authority": "EPSG", "code": 4326},
            }

        column_metadata = {
            "encoding": encoding,
            "geometry_types": geometry_types,
            "crs": crs,
        }

        # Only add bbox if we have actual data
        if bbox is not None:
            column_metadata["bbox"] = bbox

        return {
            "version": "1.1.0",  # Use official stable version
            "primary_column": geometry_column,
            "columns": {geometry_column: column_metadata},
        }

    def _calculate_bbox_from_geometries(
        self, geometries: List[Dict[str, Any]]
    ) -> List[float]:
        """
        Calculate actual bounding box from list of GeoJSON geometries.

        Args:
            geometries: List of GeoJSON geometry dictionaries

        Returns:
            Bounding box as [xmin, ymin, xmax, ymax]
        """
        if not geometries:
            return None

        min_x = min_y = float("inf")
        max_x = max_y = float("-inf")

        for geom in geometries:
            if geom is None:
                continue

            try:
                shapely_geom = shape(geom)
                bounds = shapely_geom.bounds  # (minx, miny, maxx, maxy)

                min_x = min(min_x, bounds[0])
                min_y = min(min_y, bounds[1])
                max_x = max(max_x, bounds[2])
                max_y = max(max_y, bounds[3])

            except Exception as e:
                self.logger.warning(f"Failed to get bounds for geometry: {e}")
                continue

        # Return None if no valid geometries found
        if min_x == float("inf"):
            return None

        return [min_x, min_y, max_x, max_y]

    def _detect_geometry_types(self, geometries: List[Dict[str, Any]]) -> List[str]:
        """
        Detect geometry types from actual GeoJSON geometries.

        Args:
            geometries: List of GeoJSON geometry dictionaries

        Returns:
            List of unique geometry types found
        """
        geometry_types = set()

        for geom in geometries:
            if geom is None:
                continue

            geom_type = geom.get("type")
            if geom_type:
                geometry_types.add(geom_type)

        return sorted(list(geometry_types))

    def convert_stac_geometry_to_geoarrow(
        self, stac_item: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Convert STAC item geometry to GeoArrow format with bbox struct.

        Args:
            stac_item: STAC item dictionary

        Returns:
            Dictionary with GeoArrow geometry and bbox struct
        """
        result = {}

        # Extract geometry
        geometry = stac_item.get("geometry")
        if geometry:
            # Convert to GeoArrow WKB
            result["geometry"] = self.geojson_to_wkb_array([geometry])[0].as_py()

            # Extract bbox as struct
            bbox = self.extract_bbox_from_geojson(geometry)
            if bbox:
                result["bbox"] = {
                    "xmin": bbox[0],
                    "ymin": bbox[1],
                    "xmax": bbox[2],
                    "ymax": bbox[3],
                }
            else:
                result["bbox"] = {
                    "xmin": None,
                    "ymin": None,
                    "xmax": None,
                    "ymax": None,
                }
        else:
            result["geometry"] = None
            result["bbox"] = {"xmin": None, "ymin": None, "xmax": None, "ymax": None}

        return result

    def create_geoarrow_table(
        self, records: List[Dict[str, Any]], schema: pa.Schema
    ) -> pa.Table:
        """
        Create PyArrow table with proper GeoArrow encoding.

        Args:
            records: List of record dictionaries
            schema: PyArrow schema with GeoArrow types

        Returns:
            PyArrow table with GeoArrow encoding
        """
        # Extract geometries and bboxes
        geometries = [record.get("geometry") for record in records]
        bboxes = [record.get("bbox") for record in records]

        # Convert geometries to GeoArrow
        if not any(geom is not None for geom in geometries):
            # All nulls: construct a typed null array matching GeoArrow WKB extension
            geometry_array = pa.array([None] * len(geometries), type=ga.wkb())
        elif any(isinstance(geom, dict) for geom in geometries if geom is not None):
            # GeoJSON geometries - convert to GeoArrow
            geometry_array = self.geojson_to_geoarrow(geometries)
        else:
            # Already WKB bytes or None - create WKB array
            geometry_array = ga.as_wkb(geometries)

        # Convert bboxes to struct array
        bbox_tuples = []
        for bbox in bboxes:
            if isinstance(bbox, dict) and all(
                k in bbox for k in ["xmin", "ymin", "xmax", "ymax"]
            ):
                bbox_tuples.append(
                    (bbox["xmin"], bbox["ymin"], bbox["xmax"], bbox["ymax"])
                )
            else:
                bbox_tuples.append(None)

        bbox_array = self.create_bbox_struct_array(bbox_tuples)

        # Create table data
        table_data = {}
        for field in schema:
            if field.name == "geometry":
                table_data[field.name] = geometry_array
            elif field.name == "bbox":
                table_data[field.name] = bbox_array
            elif field.name == "datetime":
                # Normalize datetime to Python datetime objects for Arrow casting
                from datetime import datetime as _dt

                values = []
                for record in records:
                    v = record.get("datetime")
                    if isinstance(v, str):
                        try:
                            v = _dt.fromisoformat(v.replace("Z", "+00:00"))
                        except Exception:
                            v = None
                    values.append(v)
                table_data[field.name] = values
            else:
                table_data[field.name] = [record.get(field.name) for record in records]

        return pa.table(table_data, schema=schema)

    def create_native_geoarrow_array(
        self, geometries: List[Dict[str, Any]], geometry_type: str = "auto"
    ) -> pa.Array:
        """
        Create native GeoArrow array (not WKB) for optimal performance.

        Args:
            geometries: List of GeoJSON geometry dictionaries
            geometry_type: Target geometry type ("point", "linestring", "polygon", "auto")

        Returns:
            PyArrow array with native GeoArrow encoding
        """
        try:
            # Convert to Shapely geometries
            shapely_geoms = []
            for geom in geometries:
                if geom is None:
                    shapely_geoms.append(None)
                else:
                    shapely_geoms.append(shape(geom))

            # Create GeoSeries
            gdf = gpd.GeoSeries(shapely_geoms)

            # Convert to native GeoArrow based on geometry type
            if geometry_type == "auto":
                # Let geoarrow determine the best encoding
                return ga.as_geoarrow(gdf)
            else:
                # Force specific geometry type if needed
                return ga.as_geoarrow(gdf)

        except Exception as e:
            self.logger.error(f"Failed to create native GeoArrow array: {e}")
            # Fallback to WKB
            return self.geojson_to_wkb_array(geometries)

    def optimize_geoarrow_for_parquet(
        self,
        geometries: List[Dict[str, Any]],
        force_wkb: bool = False,
        gpq_compatible: bool = False,
    ) -> pa.Array:
        """
        Create GeoArrow array optimized for Parquet storage.

        Args:
            geometries: List of GeoJSON geometry dictionaries
            force_wkb: If True, always use WKB encoding for compatibility
            gpq_compatible: If True, create plain binary arrays compatible with GPQ

        Returns:
            PyArrow array with appropriate encoding
        """
        try:
            if force_wkb or gpq_compatible:
                # Use WKB for compatibility
                if gpq_compatible:
                    # Create plain binary array without GeoArrow extension type
                    return self._create_plain_wkb_array(geometries)
                else:
                    # Use GeoArrow WKB extension type
                    return self.geojson_to_wkb_array(geometries)
            else:
                # Default to native GeoArrow for best performance
                complexity_score = self._analyze_geometry_complexity(geometries)

                if complexity_score < 0.3:
                    # Simple geometries - use native GeoArrow
                    self.logger.info(
                        "Using native GeoArrow encoding for simple geometries"
                    )
                    return self.geojson_to_geoarrow(geometries)
                else:
                    # Complex geometries - fallback to WKB
                    self.logger.info("Using WKB encoding for complex geometries")
                    return self.geojson_to_wkb_array(geometries)

        except Exception as e:
            self.logger.error(f"Failed to optimize GeoArrow for Parquet: {e}")
            # Fallback to WKB on any error
            return self.geojson_to_wkb_array(geometries)

    def _create_plain_wkb_array(self, geometries: List[Dict[str, Any]]) -> pa.Array:
        """
        Create a plain binary array with WKB geometries (no GeoArrow extension type).
        This is compatible with GPQ and other tools that don't understand GeoArrow extensions.

        Args:
            geometries: List of GeoJSON geometry dictionaries

        Returns:
            Plain PyArrow binary array with WKB data
        """
        try:
            from shapely.geometry import shape

            wkb_geometries = []
            for geom in geometries:
                shapely_geom = shape(geom)
                wkb_geometries.append(shapely_geom.wkb)

            # Create plain binary array (no extension type)
            return pa.array(wkb_geometries, type=pa.binary())

        except Exception as e:
            self.logger.error(f"Failed to create plain WKB array: {e}")
            raise

    def _analyze_geometry_complexity(self, geometries: List[Dict[str, Any]]) -> float:
        """
        Analyze geometry complexity to choose optimal encoding.

        Args:
            geometries: List of GeoJSON geometry dictionaries

        Returns:
            Complexity score between 0.0 (simple) and 1.0 (complex)
        """
        if not geometries:
            return 0.0

        total_score = 0.0
        valid_geoms = 0

        for geom in geometries:
            if geom is None:
                continue

            valid_geoms += 1
            geom_type = geom.get("type", "").lower()

            # Score based on geometry type complexity
            if geom_type == "point":
                total_score += 0.1
            elif geom_type == "linestring":
                coords = geom.get("coordinates", [])
                total_score += min(0.3, len(coords) / 100.0)
            elif geom_type == "polygon":
                coords = geom.get("coordinates", [])
                if coords:
                    # Score based on number of rings and vertices
                    total_score += min(0.6, len(coords) * 0.1 + len(coords[0]) / 200.0)
                else:
                    total_score += 0.3
            elif geom_type.startswith("multi"):
                total_score += 0.8  # Multi-geometries are complex
            else:
                total_score += 0.5  # Unknown types

        return total_score / max(valid_geoms, 1)

    def create_geoparquet_with_geoarrow(
        self,
        records: List[Dict[str, Any]],
        output_path: str,
        use_native_encoding: bool = True,
    ):
        """
        Create GeoParquet file with optimal GeoArrow encoding.

        Args:
            records: List of record dictionaries with geometry
            output_path: Path to output Parquet file
            use_native_encoding: Whether to use native GeoArrow vs WKB
        """
        try:
            # Extract geometries
            geometries = [record.get("geometry") for record in records]

            # Choose encoding strategy
            if use_native_encoding:
                geometry_array = self.optimize_geoarrow_for_parquet(geometries)
            else:
                geometry_array = self.geojson_to_wkb_array(geometries)

            # Create bbox arrays
            bboxes = []
            for geom in geometries:
                if geom:
                    bbox = self.extract_bbox_from_geojson(geom)
                    bboxes.append(bbox)
                else:
                    bboxes.append(None)

            bbox_array = self.create_bbox_struct_array(bboxes)

            # Create table
            table_data = {"geometry": geometry_array, "bbox": bbox_array}

            # Add other columns
            for key in records[0].keys():
                if key not in ["geometry", "bbox"]:
                    table_data[key] = [record.get(key) for record in records]

            table = pa.table(table_data)

            # Add GeoParquet metadata with actual data
            encoding = "WKB" if not use_native_encoding else "WKB"  # Force WKB for now
            metadata = self.create_geoparquet_metadata(
                geometry_column="geometry", geometries=geometries, encoding=encoding
            )

            # Write to Parquet with GeoParquet metadata
            import pyarrow.parquet as pq

            # Add metadata to schema
            schema = table.schema
            geo_metadata = json.dumps(metadata)
            schema = schema.with_metadata({"geo": geo_metadata})
            table = table.cast(schema)

            pq.write_table(table, output_path)
            self.logger.info(
                f"Created GeoParquet file with GeoArrow encoding: {output_path}"
            )

        except Exception as e:
            self.logger.error(f"Failed to create GeoParquet with GeoArrow: {e}")
            raise
