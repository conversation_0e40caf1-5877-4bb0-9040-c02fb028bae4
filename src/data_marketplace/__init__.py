# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""
Data Marketplace - Scalable STAC data indexing and querying system.

This package provides unified STAC ingestion with Delta Lake storage,
S2 spatial indexing, and DuckDB-based querying for geospatial data collections.
"""

__version__ = "0.1.0"

from importlib.metadata import version as get_version

from data_marketplace.config.settings import Settings
from data_marketplace.storage.delta_manager import DeltaManager
from data_marketplace.spatial.s2_utils import S2Utils
from data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester

# Set up logging
import logging

logging.basicConfig(
    level=logging.INFO, format="%(asctime)s - %(name)s - %(levelname)s - %(message)s"
)


def version():
    """Return the version of the data-marketplace package."""
    try:
        return get_version("data-marketplace")
    except Exception:
        return __version__


__all__ = [
    "Settings",
    "DeltaManager",
    "S2Utils",
    "DeltaStacIngester",
    "version",
]
