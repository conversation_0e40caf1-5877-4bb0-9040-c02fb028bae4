# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Lookup Table Manager for COG metadata human-readable conversions.

This module provides a hybrid approach for converting machine-readable COG metadata
to human-readable formats:

1. Static mappings (in-memory) for stable TIFF specifications
2. Dynamic lookup tables (Delta Lake) for extensible mappings like CRS codes
3. Caching for performance optimization

Usage:
    manager = LookupTableManager()
    
    # Convert single values
    compression_name = manager.get_compression_name(8)  # "deflate"
    crs_name = await manager.get_crs_name(32612)  # "WGS 84 / UTM zone 12N"
    
    # Convert full record
    human_readable = await manager.convert_record(cog_record)
"""

import asyncio
import logging
from typing import Dict, Optional, Any, List
from functools import lru_cache

try:
    import deltalake as dl
    DELTA_AVAILABLE = True
except ImportError:
    DELTA_AVAILABLE = False

from .tiff_mappings import (
    get_compression_name,
    get_sample_format_name, 
    get_predictor_name,
    build_dtype_string,
    COMMON_CRS_MAPPING
)

logger = logging.getLogger(__name__)


class LookupTableManager:
    """
    Manages lookup tables for converting machine-readable COG metadata to human-readable format.
    
    Uses hybrid approach:
    - Static mappings for TIFF specifications (compression, data types, predictor)
    - Dynamic lookup table for CRS codes (stored in Delta Lake)
    - LRU cache for performance
    """
    
    def __init__(self, crs_lookup_table_path: Optional[str] = None):
        """
        Initialize lookup table manager.
        
        Args:
            crs_lookup_table_path: Path to Delta Lake table with CRS mappings
        """
        self.crs_lookup_table_path = crs_lookup_table_path
        self._crs_cache: Dict[int, str] = {}
        self._cache_loaded = False
    
    # Static mappings (always available)
    
    def get_compression_name(self, code: int) -> str:
        """Get human-readable compression name from numeric code."""
        return get_compression_name(code)
    
    def get_sample_format_name(self, code: int) -> str:
        """Get human-readable sample format name from numeric code."""
        return get_sample_format_name(code)
    
    def get_predictor_name(self, code: int) -> str:
        """Get human-readable predictor name from numeric code."""
        return get_predictor_name(code)
    
    def build_dtype_string(self, dtype_code: int, bits_per_sample: int) -> str:
        """Build complete data type string from sample format code and bit depth."""
        return build_dtype_string(dtype_code, bits_per_sample)
    
    # Dynamic CRS mappings
    
    @lru_cache(maxsize=1000)
    def get_crs_name_static(self, code: int) -> str:
        """Get CRS name from static mapping (cached)."""
        return COMMON_CRS_MAPPING.get(code, f"EPSG:{code}")
    
    async def load_crs_lookup_table(self) -> None:
        """Load CRS lookup table from Delta Lake (if available)."""
        if not DELTA_AVAILABLE or not self.crs_lookup_table_path:
            logger.debug("Delta Lake not available or no CRS lookup table path provided")
            return
        
        try:
            # Load CRS lookup table from Delta Lake
            dt = dl.DeltaTable(self.crs_lookup_table_path)
            df = dt.to_pandas()
            
            # Convert to dictionary for fast lookup
            self._crs_cache = dict(zip(df['epsg_code'], df['crs_name']))
            self._cache_loaded = True
            
            logger.info(f"Loaded {len(self._crs_cache)} CRS mappings from Delta Lake")
            
        except Exception as e:
            logger.warning(f"Failed to load CRS lookup table: {e}")
            self._cache_loaded = False
    
    async def get_crs_name(self, code: int) -> str:
        """
        Get CRS name from dynamic lookup table or static fallback.
        
        Args:
            code: EPSG code
            
        Returns:
            Human-readable CRS name
        """
        # Try dynamic lookup first (if loaded)
        if self._cache_loaded and code in self._crs_cache:
            return self._crs_cache[code]
        
        # Fallback to static mapping
        return self.get_crs_name_static(code)
    
    # Bulk conversion methods
    
    async def convert_record(self, cog_record: Dict[str, Any]) -> Dict[str, str]:
        """
        Convert machine-readable COG record to human-readable format.
        
        Args:
            cog_record: Dictionary with machine-readable COG fields from Delta Lake
            
        Returns:
            Dictionary with human-readable field descriptions
        """
        human_readable = {}
        
        # Data type
        dtype_code = cog_record.get('cog_dtype_code')
        bits_per_sample = cog_record.get('cog_bits_per_sample')
        if dtype_code is not None and bits_per_sample is not None:
            human_readable['data_type'] = self.build_dtype_string(dtype_code, bits_per_sample)
        
        # Compression
        compression_code = cog_record.get('cog_compression_code')
        if compression_code is not None:
            human_readable['compression'] = self.get_compression_name(compression_code)
        
        # Predictor
        predictor = cog_record.get('cog_predictor')
        if predictor is not None:
            human_readable['predictor'] = self.get_predictor_name(predictor)
        
        # CRS (async lookup)
        crs_code = cog_record.get('cog_crs_code')
        if crs_code is not None:
            human_readable['coordinate_system'] = await self.get_crs_name(crs_code)
        
        # Dimensions (pass through)
        if cog_record.get('cog_width') is not None:
            human_readable['dimensions'] = f"{cog_record['cog_width']}x{cog_record['cog_height']}"
        
        # Tile size (pass through)
        tile_width = cog_record.get('cog_tile_width')
        tile_height = cog_record.get('cog_tile_height')
        if tile_width is not None and tile_height is not None:
            human_readable['tile_size'] = f"{tile_width}x{tile_height}"
        
        return human_readable
    
    async def convert_records_batch(self, cog_records: List[Dict[str, Any]]) -> List[Dict[str, str]]:
        """
        Convert multiple COG records to human-readable format efficiently.
        
        Args:
            cog_records: List of machine-readable COG records
            
        Returns:
            List of human-readable dictionaries
        """
        # Pre-load CRS cache if not already loaded
        if not self._cache_loaded:
            await self.load_crs_lookup_table()
        
        # Convert all records
        tasks = [self.convert_record(record) for record in cog_records]
        return await asyncio.gather(*tasks)


# Global instance for easy access
_lookup_manager: Optional[LookupTableManager] = None


def get_lookup_manager(crs_lookup_table_path: Optional[str] = None) -> LookupTableManager:
    """
    Get global lookup table manager instance.
    
    Args:
        crs_lookup_table_path: Path to Delta Lake CRS lookup table
        
    Returns:
        LookupTableManager instance
    """
    global _lookup_manager
    
    if _lookup_manager is None:
        _lookup_manager = LookupTableManager(crs_lookup_table_path)
    
    return _lookup_manager


# Convenience functions for backward compatibility
async def convert_cog_record_to_human_readable(
    cog_record: Dict[str, Any], 
    crs_lookup_table_path: Optional[str] = None
) -> Dict[str, str]:
    """
    Convert single COG record to human-readable format.
    
    Args:
        cog_record: Machine-readable COG record from Delta Lake
        crs_lookup_table_path: Optional path to CRS lookup table
        
    Returns:
        Human-readable field descriptions
    """
    manager = get_lookup_manager(crs_lookup_table_path)
    return await manager.convert_record(cog_record)


async def convert_cog_records_batch(
    cog_records: List[Dict[str, Any]], 
    crs_lookup_table_path: Optional[str] = None
) -> List[Dict[str, str]]:
    """
    Convert multiple COG records to human-readable format.
    
    Args:
        cog_records: List of machine-readable COG records
        crs_lookup_table_path: Optional path to CRS lookup table
        
    Returns:
        List of human-readable dictionaries
    """
    manager = get_lookup_manager(crs_lookup_table_path)
    return await manager.convert_records_batch(cog_records)
