# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
STAC-specific COG processing functionality.

This module extends the existing COG parser to handle STAC-specific
COG processing workflows and asset management.
"""

import logging
from typing import List, Dict, Any, Optional, Tuple
from pystac import Item

from data_marketplace.cog.cog_parser import COGHeaderParser

logger = logging.getLogger(__name__)


class StacCogProcessor:
    """Handles COG processing for STAC items."""

    def __init__(self):
        """Initialize STAC COG processor with high-performance parser."""
        # No parser selection needed - always use the optimized parser
        pass

    def compute_request_href(self, href: str) -> str:
        """Return an HTTP(S) URL suitable for header requests.

        - If href is already http(s), return as-is
        - If href is s3://bucket/key, convert to https://bucket.s3.amazonaws.com/key
        - Otherwise return href unchanged
        """
        try:
            h = str(href)
            if h.startswith("http://") or h.startswith("https://"):
                return h
            if h.startswith("s3://"):
                # Simple virtual-hosted–style URL. Public buckets will work; private will 403.
                # s3://bucket/path/to.tif -> https://bucket.s3.amazonaws.com/path/to.tif
                without = h[len("s3://") :]
                if "/" in without:
                    bucket, key = without.split("/", 1)
                else:
                    bucket, key = without, ""
                return f"https://{bucket}.s3.amazonaws.com/{key}"
        except Exception:
            pass
        return str(href)

    async def parse_cog_headers_for_item(
        self,
        stac_item: Item,
        max_concurrent_requests: int,
        only_keys: Optional[set] = None,
    ) -> List[Dict[str, Any]]:
        """
        Fast COG header parsing for bulk scenarios.

        Uses simplified parsing for maximum throughput.
        """
        cog_records = []

        # Filter for COG assets
        cog_assets = {
            key: asset
            for key, asset in stac_item.assets.items()
            if (only_keys is None or key in only_keys) and self.is_cog_asset(asset)
        }

        if not cog_assets:
            return cog_records

        # Extract URLs for batch processing
        urls = [self.compute_request_href(asset.href) for asset in cog_assets.values()]

        # Use high-performance parser for batch processing
        async with COGHeaderParser(max_concurrent=max_concurrent_requests) as parser:
            results = await parser.parse_batch(urls)

        # Convert results to COG records
        for i, (asset_key, asset) in enumerate(cog_assets.items()):
            result = results[i] if i < len(results) else None

            if result and not isinstance(result, Exception):
                # Convert FastCogMetadata to standard format
                cog_meta = result.to_dict()

                # Extract scale/offset from asset metadata if available
                scale, offset = self.extract_scale_offset_from_asset(asset)
                if scale is not None and "cog_scale" not in cog_meta:
                    cog_meta["cog_scale"] = scale
                if offset is not None and "cog_offset" not in cog_meta:
                    cog_meta["cog_offset"] = offset

                # Create enhanced COG record (same format as regular parser)
                cog_record = {
                    "asset_key": asset_key,
                    "asset_href": str(asset.href),
                    "cog_key": asset_key,
                    "cog_href": str(asset.href),
                    "asset_media_type": asset.media_type,
                    "asset_roles": list(asset.roles) if asset.roles else [],
                    "asset_title": asset.title,
                    "asset_description": asset.description,
                    **cog_meta,
                }

                cog_records.append(cog_record)

        logger.debug(
            f"Fast parsed {len(cog_records)} COG assets for STAC item {stac_item.id}"
        )
        return cog_records

    async def parse_cog_headers_for_asset(
        self, asset_href: str, asset_key: str, asset_data: dict
    ) -> List[Dict[str, Any]]:
        """
        Parse COG headers for a single asset.

        This method is used by the streaming consumer to process individual assets.

        Args:
            asset_href: URL of the asset
            asset_key: Key/name of the asset
            asset_data: Asset metadata dictionary

        Returns:
            List containing a single COG record dictionary
        """
        try:
            # Convert asset_href to request URL
            url = self.compute_request_href(asset_href)

            # Use high-performance parser for single asset
            async with COGHeaderParser(max_concurrent=1) as parser:
                result = await parser.parse_cog_header(url)

            if result and not isinstance(result, Exception):
                # Convert FastCogMetadata to standard format
                cog_meta = result.to_dict()

                # Create enhanced COG record
                cog_record = {
                    "asset_key": asset_key,
                    "asset_href": asset_href,
                    "cog_key": asset_key,
                    "cog_href": asset_href,
                    "asset_media_type": asset_data.get("type"),
                    "asset_roles": asset_data.get("roles", []),
                    "asset_title": asset_data.get("title"),
                    "asset_description": asset_data.get("description"),
                    **cog_meta,
                }

                return [cog_record]
            else:
                logger.debug(f"Failed to parse COG header for {asset_href}")
                return []

        except Exception as e:
            logger.error(f"Error parsing COG header for asset {asset_key}: {e}")
            return []

    def extract_scale_offset_from_asset(
        self, asset
    ) -> Tuple[Optional[float], Optional[float]]:
        """
        Extract scale and offset from asset raster:bands extension.

        Args:
            asset: STAC asset object

        Returns:
            Tuple of (scale, offset) or (None, None) if not found
        """
        try:
            raster_bands = asset.extra_fields.get("raster:bands", [])
            if (
                raster_bands
                and isinstance(raster_bands, list)
                and len(raster_bands) > 0
            ):
                first_band = raster_bands[0]
                scale = first_band.get("scale", None)
                offset = first_band.get("offset", None)

                if scale is not None or offset is not None:
                    logger.debug(f"Found scale={scale}, offset={offset} for asset")
                    return scale, offset
        except Exception as e:
            logger.debug(f"Error extracting scale/offset from asset: {e}")

        return None, None

    def is_cog_asset(self, asset) -> bool:
        """
        Check if asset is a COG asset.

        Args:
            asset: STAC asset object

        Returns:
            True if asset is a COG, False otherwise
        """
        if not getattr(asset, "href", None):
            return False

        href = str(asset.href).lower()
        media = str(getattr(asset, "media_type", "")).lower()

        # Accept HTTP(S) GeoTIFF/COG assets and ignore JP2
        if not (href.startswith("http://") or href.startswith("https://")):
            return False

        # Accept common GeoTIFF media types; tolerate missing media_type if extension matches
        geotiff_types = {
            "image/tiff",
            "image/tiff; application=geotiff",
            "image/tiff; profile=cloud-optimized",
            "image/geotiff",
        }
        if (media and media not in geotiff_types) and not href.endswith(
            (".tif", ".tiff")
        ):
            return False

        # Exclude JP2 QA assets explicitly
        if href.endswith(".jp2") or media == "image/jp2":
            return False

        return True

    def get_cog_asset_priority(self, asset_key: str, asset=None) -> int:
        """
        Get priority for COG asset processing (lower number = higher priority).

        Args:
            asset_key: Asset key name
            asset: STAC asset object

        Returns:
            Priority value (0 = highest priority)
        """
        # Prioritize certain asset types
        high_priority_keys = ["red", "green", "blue", "nir", "B04", "B03", "B02", "B08"]
        medium_priority_keys = ["swir", "B11", "B12", "coastal", "B01"]

        if asset_key.lower() in [k.lower() for k in high_priority_keys]:
            return 0
        elif asset_key.lower() in [k.lower() for k in medium_priority_keys]:
            return 1
        else:
            return 2

    def filter_priority_assets(
        self, assets: Dict[str, Any], max_assets: int = 10
    ) -> Dict[str, Any]:
        """
        Filter assets by priority to limit processing load.

        Args:
            assets: Dictionary of asset key -> asset object
            max_assets: Maximum number of assets to process

        Returns:
            Filtered dictionary of assets
        """
        if len(assets) <= max_assets:
            return assets

        # Sort by priority
        sorted_assets = sorted(
            assets.items(), key=lambda x: self.get_cog_asset_priority(x[0], x[1])
        )

        # Take top priority assets
        return dict(sorted_assets[:max_assets])
