# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
TIFF/COG field mappings for converting machine-readable codes to human-readable values.

This module provides lookup tables for converting numeric TIFF codes stored in Delta Lake
to human-readable descriptions for analysis, reporting, and user interfaces.

These mappings are based on:
- TIFF 6.0 Specification
- GeoTIFF Format Specification  
- COG Specification
- async-tiff library enums
"""

from typing import Dict, Optional


# TIFF Sample Format Codes (Tag 339)
# Source: TIFF 6.0 Specification, Section 19
SAMPLE_FORMAT_MAPPING: Dict[int, str] = {
    1: "uint",      # Unsigned integer data
    2: "int",       # Two's complement signed integer data
    3: "float",     # IEEE floating point data
    4: "undefined", # Undefined data format
    5: "complex_int",   # Complex integer (not commonly used)
    6: "complex_float", # Complex IEEE floating point (not commonly used)
}

def get_sample_format_name(code: int) -> str:
    """Get human-readable sample format name from numeric code."""
    return SAMPLE_FORMAT_MAPPING.get(code, f"unknown_{code}")


# TIFF Compression Codes (Tag 259)
# Source: TIFF 6.0 Specification + common extensions
COMPRESSION_MAPPING: Dict[int, str] = {
    1: "none",          # No compression
    2: "ccitt_rle",     # CCITT modified Huffman RLE
    3: "ccitt_fax3",    # CCITT Group 3 fax encoding
    4: "ccitt_fax4",    # CCITT Group 4 fax encoding
    5: "lzw",           # Lempel-Ziv & Welch
    6: "jpeg_old",      # JPEG (old-style)
    7: "jpeg",          # JPEG DCT compression
    8: "deflate",       # Adobe Deflate (ZIP)
    9: "jbig_bw",       # JBIG (ITU-T T.82)
    10: "jbig_color",   # JBIG (ITU-T T.82) color
    32766: "next",      # NeXT 2-bit RLE
    32771: "ccitt_rlew", # CCITT RLE with word alignment
    32773: "packbits",  # Macintosh RLE
    32809: "thunderscan", # ThunderScan RLE
    32895: "it8ctpad",  # IT8 CT w/padding
    32896: "it8lw",     # IT8 Linework RLE
    32897: "it8mp",     # IT8 Monochrome picture
    32898: "it8bl",     # IT8 Binary line art
    32908: "pixarfilm", # Pixar companded 10bit LZW
    32909: "pixarlog",  # Pixar companded 11bit ZIP
    32946: "deflate_pk", # PKWare Deflate
    32947: "dcs",       # Kodak DCS encoding
    34661: "jbig",      # ISO JBIG
    34676: "sgilog",    # SGI Log Luminance RLE
    34677: "sgilog24",  # SGI Log 24-bit packed
    34712: "jp2000",    # JPEG 2000
    34887: "lerc",      # ESRI Lerc codec
    50000: "zstd",      # Zstandard
    50001: "webp",      # WebP
}

def get_compression_name(code: int) -> str:
    """Get human-readable compression name from numeric code."""
    return COMPRESSION_MAPPING.get(code, f"unknown_{code}")


# TIFF Predictor Codes (Tag 317)
# Source: TIFF 6.0 Specification, Section 14
PREDICTOR_MAPPING: Dict[int, str] = {
    1: "none",          # No prediction scheme used
    2: "horizontal",    # Horizontal differencing
    3: "floating_point", # Floating point predictor
}

def get_predictor_name(code: int) -> str:
    """Get human-readable predictor name from numeric code."""
    return PREDICTOR_MAPPING.get(code, f"unknown_{code}")


# Common EPSG Codes for CRS
# Source: EPSG Geodetic Parameter Dataset
# Note: This is a subset of commonly used codes. Full EPSG database has 6000+ codes.
COMMON_CRS_MAPPING: Dict[int, str] = {
    # Geographic Coordinate Systems
    4326: "WGS 84",
    4269: "NAD83",
    4267: "NAD27",
    4258: "ETRS89",
    4230: "ED50",
    
    # UTM Zones (WGS 84) - Northern Hemisphere
    32601: "WGS 84 / UTM zone 1N",
    32602: "WGS 84 / UTM zone 2N",
    32603: "WGS 84 / UTM zone 3N",
    32604: "WGS 84 / UTM zone 4N",
    32605: "WGS 84 / UTM zone 5N",
    32606: "WGS 84 / UTM zone 6N",
    32607: "WGS 84 / UTM zone 7N",
    32608: "WGS 84 / UTM zone 8N",
    32609: "WGS 84 / UTM zone 9N",
    32610: "WGS 84 / UTM zone 10N",
    32611: "WGS 84 / UTM zone 11N",
    32612: "WGS 84 / UTM zone 12N",
    32613: "WGS 84 / UTM zone 13N",
    32614: "WGS 84 / UTM zone 14N",
    32615: "WGS 84 / UTM zone 15N",
    32616: "WGS 84 / UTM zone 16N",
    32617: "WGS 84 / UTM zone 17N",
    32618: "WGS 84 / UTM zone 18N",
    32619: "WGS 84 / UTM zone 19N",
    32620: "WGS 84 / UTM zone 20N",
    32621: "WGS 84 / UTM zone 21N",
    32622: "WGS 84 / UTM zone 22N",
    32623: "WGS 84 / UTM zone 23N",
    32624: "WGS 84 / UTM zone 24N",
    32625: "WGS 84 / UTM zone 25N",
    32626: "WGS 84 / UTM zone 26N",
    32627: "WGS 84 / UTM zone 27N",
    32628: "WGS 84 / UTM zone 28N",
    32629: "WGS 84 / UTM zone 29N",
    32630: "WGS 84 / UTM zone 30N",
    32631: "WGS 84 / UTM zone 31N",
    32632: "WGS 84 / UTM zone 32N",
    32633: "WGS 84 / UTM zone 33N",
    32634: "WGS 84 / UTM zone 34N",
    32635: "WGS 84 / UTM zone 35N",
    32636: "WGS 84 / UTM zone 36N",
    32637: "WGS 84 / UTM zone 37N",
    32638: "WGS 84 / UTM zone 38N",
    32639: "WGS 84 / UTM zone 39N",
    32640: "WGS 84 / UTM zone 40N",
    32641: "WGS 84 / UTM zone 41N",
    32642: "WGS 84 / UTM zone 42N",
    32643: "WGS 84 / UTM zone 43N",
    32644: "WGS 84 / UTM zone 44N",
    32645: "WGS 84 / UTM zone 45N",
    32646: "WGS 84 / UTM zone 46N",
    32647: "WGS 84 / UTM zone 47N",
    32648: "WGS 84 / UTM zone 48N",
    32649: "WGS 84 / UTM zone 49N",
    32650: "WGS 84 / UTM zone 50N",
    32651: "WGS 84 / UTM zone 51N",
    32652: "WGS 84 / UTM zone 52N",
    32653: "WGS 84 / UTM zone 53N",
    32654: "WGS 84 / UTM zone 54N",
    32655: "WGS 84 / UTM zone 55N",
    32656: "WGS 84 / UTM zone 56N",
    32657: "WGS 84 / UTM zone 57N",
    32658: "WGS 84 / UTM zone 58N",
    32659: "WGS 84 / UTM zone 59N",
    32660: "WGS 84 / UTM zone 60N",
    
    # UTM Zones (WGS 84) - Southern Hemisphere
    32701: "WGS 84 / UTM zone 1S",
    32702: "WGS 84 / UTM zone 2S",
    32703: "WGS 84 / UTM zone 3S",
    32704: "WGS 84 / UTM zone 4S",
    32705: "WGS 84 / UTM zone 5S",
    32706: "WGS 84 / UTM zone 6S",
    32707: "WGS 84 / UTM zone 7S",
    32708: "WGS 84 / UTM zone 8S",
    32709: "WGS 84 / UTM zone 9S",
    32710: "WGS 84 / UTM zone 10S",
    32711: "WGS 84 / UTM zone 11S",
    32712: "WGS 84 / UTM zone 12S",
    32713: "WGS 84 / UTM zone 13S",
    32714: "WGS 84 / UTM zone 14S",
    32715: "WGS 84 / UTM zone 15S",
    32716: "WGS 84 / UTM zone 16S",
    32717: "WGS 84 / UTM zone 17S",
    32718: "WGS 84 / UTM zone 18S",
    32719: "WGS 84 / UTM zone 19S",
    32720: "WGS 84 / UTM zone 20S",
    32721: "WGS 84 / UTM zone 21S",
    32722: "WGS 84 / UTM zone 22S",
    32723: "WGS 84 / UTM zone 23S",
    32724: "WGS 84 / UTM zone 24S",
    32725: "WGS 84 / UTM zone 25S",
    32726: "WGS 84 / UTM zone 26S",
    32727: "WGS 84 / UTM zone 27S",
    32728: "WGS 84 / UTM zone 28S",
    32729: "WGS 84 / UTM zone 29S",
    32730: "WGS 84 / UTM zone 30S",
    32731: "WGS 84 / UTM zone 31S",
    32732: "WGS 84 / UTM zone 32S",
    32733: "WGS 84 / UTM zone 33S",
    32734: "WGS 84 / UTM zone 34S",
    32735: "WGS 84 / UTM zone 35S",
    32736: "WGS 84 / UTM zone 36S",
    32737: "WGS 84 / UTM zone 37S",
    32738: "WGS 84 / UTM zone 38S",
    32739: "WGS 84 / UTM zone 39S",
    32740: "WGS 84 / UTM zone 40S",
    32741: "WGS 84 / UTM zone 41S",
    32742: "WGS 84 / UTM zone 42S",
    32743: "WGS 84 / UTM zone 43S",
    32744: "WGS 84 / UTM zone 44S",
    32745: "WGS 84 / UTM zone 45S",
    32746: "WGS 84 / UTM zone 46S",
    32747: "WGS 84 / UTM zone 47S",
    32748: "WGS 84 / UTM zone 48S",
    32749: "WGS 84 / UTM zone 49S",
    32750: "WGS 84 / UTM zone 50S",
    32751: "WGS 84 / UTM zone 51S",
    32752: "WGS 84 / UTM zone 52S",
    32753: "WGS 84 / UTM zone 53S",
    32754: "WGS 84 / UTM zone 54S",
    32755: "WGS 84 / UTM zone 55S",
    32756: "WGS 84 / UTM zone 56S",
    32757: "WGS 84 / UTM zone 57S",
    32758: "WGS 84 / UTM zone 58S",
    32759: "WGS 84 / UTM zone 59S",
    32760: "WGS 84 / UTM zone 60S",
    
    # State Plane Coordinate Systems (NAD83) - Common ones
    2154: "NAD83 / UTM zone 10N",
    2155: "NAD83 / UTM zone 11N",
    2156: "NAD83 / UTM zone 12N",
    2157: "NAD83 / UTM zone 13N",
    2158: "NAD83 / UTM zone 14N",
    2159: "NAD83 / UTM zone 15N",
    2160: "NAD83 / UTM zone 16N",
    2161: "NAD83 / UTM zone 17N",
    2162: "NAD83 / UTM zone 18N",
    2163: "NAD83 / UTM zone 19N",
    
    # Web Mercator
    3857: "WGS 84 / Pseudo-Mercator",
    900913: "Google Maps Global Mercator (deprecated)",
}

def get_crs_name(code: int) -> str:
    """Get human-readable CRS name from EPSG code."""
    return COMMON_CRS_MAPPING.get(code, f"EPSG:{code}")


def build_dtype_string(dtype_code: int, bits_per_sample: int) -> str:
    """Build complete data type string from sample format code and bit depth."""
    format_name = get_sample_format_name(dtype_code)
    return f"{format_name}{bits_per_sample}"


# Utility functions for converting Delta Lake values to human-readable
def get_human_readable_metadata(cog_record: Dict) -> Dict[str, str]:
    """
    Convert machine-readable COG metadata to human-readable format.
    
    Args:
        cog_record: Dictionary with machine-readable COG fields from Delta Lake
        
    Returns:
        Dictionary with human-readable field descriptions
    """
    human_readable = {}
    
    # Data type
    if cog_record.get('cog_dtype_code') is not None and cog_record.get('cog_bits_per_sample') is not None:
        human_readable['data_type'] = build_dtype_string(
            cog_record['cog_dtype_code'], 
            cog_record['cog_bits_per_sample']
        )
    
    # Compression
    if cog_record.get('cog_compression_code') is not None:
        human_readable['compression'] = get_compression_name(cog_record['cog_compression_code'])
    
    # Predictor
    if cog_record.get('cog_predictor') is not None:
        human_readable['predictor'] = get_predictor_name(cog_record['cog_predictor'])
    
    # CRS
    if cog_record.get('cog_crs_code') is not None:
        human_readable['coordinate_system'] = get_crs_name(cog_record['cog_crs_code'])
    
    return human_readable
