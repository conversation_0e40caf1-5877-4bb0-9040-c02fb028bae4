# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""STAC table schemas optimized for Sentinel-2 and other Earth observation data."""

import logging
from typing import Dict, List, Any, Optional
from dataclasses import dataclass
from datetime import datetime
import pyarrow as pa
import geoarrow.pyarrow as ga
from data_marketplace.spatial.bbox_utils import BboxUtils
from data_marketplace.spatial.geoarrow_utils import GeoArrowUtils
from data_marketplace.spatial.s2_utils import S2Utils

logger = logging.getLogger(__name__)


@dataclass
class StacSchemaConfig:
    """Configuration for STAC schema generation."""

    use_native_geoarrow: bool = True
    include_s2_indexing: bool = True
    flatten_properties: bool = True
    max_asset_keys: int = 50  # Limit for asset key arrays
    precision: int = 6  # Coordinate precision


class UnifiedStacSchema:
    """
    Unified STAC + COG schema for Earth observation data.

    Single table with one row per COG asset (band), including:
    - Scene metadata (repeated per band for optimal query performance)
    - COG-specific metadata (band-specific)
    - Native GeoArrow geometry encoding
    - S2 spatial indexing for global coverage
    - Optimized for cross-collection spatial + technical queries
    - Year/month temporal partitioning
    """

    def __init__(self, config: Optional[StacSchemaConfig] = None):
        """
        Initialize unified STAC + COG schema.

        Args:
            config: Schema configuration options
        """
        self.config = config or StacSchemaConfig()
        self.logger = logger
        self.bbox_utils = BboxUtils(precision=self.config.precision)
        self.geoarrow_utils = GeoArrowUtils()

        # Initialize S2 utils if spatial indexing is enabled
        if self.config.include_s2_indexing:
            self.s2_utils = S2Utils(cell_level=6, adaptive_levels=True)
        else:
            self.s2_utils = None

    def get_unified_schema(self) -> pa.Schema:
        """
        Get the unified STAC + COG schema (one row per COG asset).

        Returns:
            PyArrow schema for unified table
        """
        fields = [
            # STAC Core Fields (repeated per COG asset for optimal query performance)
            pa.field("scene_id", pa.string(), nullable=False),  # Scene identifier
            pa.field("collection", pa.string(), nullable=False),  # Collection name
            pa.field(
                "catalog_id", pa.string(), nullable=True
            ),  # STAC catalog identifier for multi-catalog federation
            pa.field(
                "stac_version", pa.string(), nullable=True
            ),  # STAC specification version (1.0.0, 1.1.0, etc.)
            pa.field(
                "datetime", pa.timestamp("us", tz="UTC"), nullable=False
            ),  # Observation time
            # Geometry Fields (Native GeoArrow) - repeated per asset for unified spatial queries
            pa.field(
                "geometry", self._get_geometry_type(), nullable=True
            ),  # Scene geometry
            pa.field(
                "bbox", self.bbox_utils.get_bbox_schema(), nullable=True
            ),  # 4-field bbox struct
            # Spatial Indexing - repeated per asset for unified spatial indexing
            # Note: Using string for S2 cell IDs because Delta Lake doesn't support UInt64
            pa.field(
                "s2_cell_id", pa.string(), nullable=True
            ),  # Primary S2 cell (string for Delta Lake compatibility)
            pa.field(
                "s2_cells", pa.list_(pa.string()), nullable=True
            ),  # All covering cells (strings for Delta Lake compatibility)
            pa.field("s2_level", pa.int8(), nullable=True),  # S2 cell level used
            # Common EO Properties (flattened from STAC properties) - repeated per asset
            pa.field("platform", pa.string(), nullable=True),  # Satellite platform
            pa.field(
                "instruments", pa.list_(pa.string()), nullable=True
            ),  # Instrument names
            pa.field(
                "constellation", pa.string(), nullable=True
            ),  # Satellite constellation
            pa.field("mission", pa.string(), nullable=True),  # Mission name
            # Observation Properties - repeated per asset
            pa.field("gsd", pa.float64(), nullable=True),  # Ground sample distance (m)
            pa.field(
                "cloud_cover", pa.float64(), nullable=True
            ),  # Cloud coverage (0-100)
            pa.field("sun_azimuth", pa.float64(), nullable=True),  # Sun azimuth angle
            pa.field(
                "sun_elevation", pa.float64(), nullable=True
            ),  # Sun elevation angle
            pa.field("view_azimuth", pa.float64(), nullable=True),  # View azimuth angle
            pa.field(
                "view_off_nadir", pa.float64(), nullable=True
            ),  # View off-nadir angle
            # Processing Properties - repeated per asset
            pa.field("processing_level", pa.string(), nullable=True),  # L1C, L2A, etc.
            pa.field("product_type", pa.string(), nullable=True),  # Product type
            pa.field(
                "processing_baseline", pa.string(), nullable=True
            ),  # Processing version
            # Data Provenance and Federation Fields
            pa.field(
                "data_provider", pa.string(), nullable=True
            ),  # Data provider (AWS, Microsoft, etc.)
            pa.field(
                "stac_api_url", pa.string(), nullable=True
            ),  # Source STAC API URL for federation
            pa.field(
                "license", pa.string(), nullable=True
            ),  # Data license for compliance
            # COG Asset Fields (asset-specific, not repeated)
            pa.field(
                "cog_key", pa.string(), nullable=False
            ),  # Asset identifier (B01, B02, etc.)
            pa.field("cog_href", pa.string(), nullable=False),  # COG URL
            pa.field("cog_title", pa.string(), nullable=True),  # Asset title
            pa.field("cog_roles", pa.list_(pa.string()), nullable=True),  # Asset roles
            # COG Technical Metadata (highly repeated - dictionary encoded for massive space savings)
            pa.field("cog_width", pa.int32(), nullable=True),  # Image width
            pa.field("cog_height", pa.int32(), nullable=True),  # Image height
            pa.field("cog_tile_width", pa.int32(), nullable=True),  # Tile width
            pa.field("cog_tile_height", pa.int32(), nullable=True),  # Tile height
            pa.field("cog_dtype_code", pa.int32(), nullable=True),  # Sample format code (1=uint, 2=int, 3=float)
            pa.field("cog_bits_per_sample", pa.int32(), nullable=True),  # Bit depth (8, 16, 32, 64)
            pa.field("cog_compression_code", pa.int32(), nullable=True),  # Compression code (1=none, 8=deflate)
            pa.field("cog_predictor", pa.int32(), nullable=True),  # TIFF predictor
            # COG Spatial Metadata
            pa.field("cog_crs_code", pa.int32(), nullable=True),  # EPSG code (32612, 4326)
            pa.field(
                "cog_transform", pa.list_(pa.float64()), nullable=True
            ),  # Affine transform (6 values)
            # COG Internal Structure (large arrays - use ZSTD compression)
            pa.field(
                "cog_tile_offsets", pa.list_(pa.int64()), nullable=True
            ),  # Tile byte offsets
            pa.field(
                "cog_tile_byte_counts", pa.list_(pa.int64()), nullable=True
            ),  # Tile byte counts
            # Band-specific Metadata (Radiometric Calibration)
            pa.field("cog_dn_scale", pa.float64(), nullable=True),  # DN scale factor (from STAC metadata)
            pa.field("cog_dn_offset", pa.float64(), nullable=True),  # DN offset value (from STAC metadata)
            # Temporal Partitioning Fields
            pa.field("year", pa.int32(), nullable=False),  # Partition key
            pa.field("month", pa.int32(), nullable=False),  # Partition key
            pa.field("day", pa.int32(), nullable=True),  # For daily queries
            # Metadata
            pa.field(
                "stac_extensions", pa.list_(pa.string()), nullable=True
            ),  # STAC extensions used
            pa.field(
                "created", pa.timestamp("us", tz="UTC"), nullable=True
            ),  # Creation timestamp
            pa.field(
                "updated", pa.timestamp("us", tz="UTC"), nullable=True
            ),  # Last update timestamp
        ]

        return pa.schema(fields)

    def _get_geometry_type(self) -> pa.DataType:
        """Get the appropriate geometry data type."""
        if self.config.use_native_geoarrow:
            # Use native GeoArrow WKB extension type
            return ga.wkb()
        else:
            # Fallback to binary WKB
            return pa.binary()

    def get_bloom_filter_columns(self) -> List[str]:
        """
        Get columns that should have Bloom filters for optimal query performance.

        Returns:
            List of column names for Bloom filter indexing
        """
        return [
            "scene_id",  # Primary key lookups
            "collection",  # Collection filtering
            "cog_key",  # Asset filtering (B01, B02, etc.)
            "s2_cell_id",  # Spatial queries
            "platform",  # Platform filtering
            "constellation",  # Constellation filtering
        ]

    def get_dictionary_columns(self) -> List[str]:
        """
        Get columns that should use dictionary encoding for compression.

        Returns:
            List of column names for dictionary encoding
        """
        return [
            # Scene metadata (repeated per COG asset)
            "collection",  # Collection names - limited unique values
            "platform",  # Platform names - very few unique values
            "constellation",  # Constellation names - very few unique values
            "mission",  # Mission names - very few unique values
            "processing_level",  # Processing levels - very few unique values
            "product_type",  # Product types - limited unique values
            "stac_version",  # STAC versions - very few unique values
            # COG metadata (highly repeated values) - NOTE: Now using machine-readable codes
            "cog_key",  # Band names like 'B04', 'red', etc. - limited unique values
            # Note: cog_dtype_code, cog_compression_code, cog_crs_code are now integers (no dictionary encoding needed)
            "cog_title",  # Asset titles - some repetition
        ]

    def get_partition_columns(self) -> List[str]:
        """
        Get columns used for Delta Lake partitioning.

        Returns:
            List of partition column names
        """
        return ["year", "month"]

    def flatten_stac_item(self, stac_item: Dict[str, Any]) -> Dict[str, Any]:
        """
        Flatten a STAC item into the optimized table schema.

        Args:
            stac_item: STAC item dictionary

        Returns:
            Flattened record dictionary
        """
        try:
            # Extract core fields
            record = {
                "scene_id": stac_item["id"],
                "collection": stac_item["collection"],
                "catalog_id": stac_item.get(
                    "catalog_id"
                ),  # For multi-catalog federation
                "stac_version": stac_item.get("stac_version"),
                "stac_extensions": stac_item.get("stac_extensions", []),
            }

            # Parse datetime
            datetime_str = stac_item["properties"]["datetime"]
            if datetime_str:
                dt = datetime.fromisoformat(datetime_str.replace("Z", "+00:00"))
                record["datetime"] = dt
                record["year"] = dt.year
                record["month"] = dt.month
                record["day"] = dt.day

            # Initialize S2 fields with defaults
            record["s2_cell_id"] = None
            record["s2_level"] = None
            record["s2_cells"] = []

            # Extract geometry and bbox
            if "geometry" in stac_item and stac_item["geometry"]:
                # Convert GeoJSON geometry to WKB bytes for GeoArrow
                from shapely.geometry import shape
                from shapely import wkb

                shapely_geom = shape(stac_item["geometry"])
                record["geometry"] = wkb.dumps(shapely_geom)

                # Calculate bbox from geometry if not provided
                if "bbox" in stac_item:
                    bbox_list = stac_item["bbox"]
                    bbox_struct = self.bbox_utils.list_to_bbox_struct(bbox_list)
                    # Convert BboxStruct to dict for PyArrow compatibility
                    record["bbox"] = {
                        "xmin": bbox_struct.xmin,
                        "ymin": bbox_struct.ymin,
                        "xmax": bbox_struct.xmax,
                        "ymax": bbox_struct.ymax,
                    }

                # Add S2 spatial indexing if enabled
                if self.config.include_s2_indexing and self.s2_utils:
                    try:
                        # S2 handles antimeridian crossing and polar regions perfectly
                        # No need to validate bounds - S2 is designed for global coverage

                        # For simple geometries (most Sentinel-2), use centroid
                        if shapely_geom.geom_type in ["Polygon", "MultiPolygon"]:
                            centroid = shapely_geom.centroid
                            primary_cell = self.s2_utils.point_to_s2_cell(
                                centroid.x, centroid.y, level=6
                            )
                            # Convert S2 cell ID to string for Delta Lake compatibility
                            record["s2_cell_id"] = str(primary_cell)
                            record["s2_level"] = 6

                            # For large or complex geometries, use covering
                            area = shapely_geom.area
                            if area > 1.0:  # Large area threshold (degrees²)
                                s2_cells = self.s2_utils.polygon_to_s2_cells(
                                    shapely_geom, max_cells=10
                                )
                                # Convert S2 cell IDs to strings for Delta Lake compatibility
                                record["s2_cells"] = [
                                    str(cell_id) for cell_id in s2_cells
                                ]
                            else:
                                record["s2_cells"] = [str(primary_cell)]
                        else:
                            # For points, use direct conversion
                            coords = (
                                shapely_geom.coords[0]
                                if hasattr(shapely_geom, "coords")
                                else (0, 0)
                            )
                            primary_cell = self.s2_utils.point_to_s2_cell(
                                coords[0], coords[1], level=6
                            )
                            # Convert S2 cell ID to string for Delta Lake compatibility
                            record["s2_cell_id"] = str(primary_cell)
                            record["s2_level"] = 6
                            record["s2_cells"] = [str(primary_cell)]

                    except Exception as e:
                        self.logger.warning(
                            f"Failed to calculate S2 indexing for {record['scene_id']}: {e}"
                        )
                        record["s2_cell_id"] = None
                        record["s2_level"] = None
                        record["s2_cells"] = []
            else:
                # No geometry available - set defaults
                record["geometry"] = None
                record["bbox"] = None
                record["s2_cell_id"] = None
                record["s2_level"] = None
                record["s2_cells"] = []

            # Flatten properties
            props = stac_item.get("properties", {})

            # Common EO properties
            record["platform"] = props.get("platform")
            record["instruments"] = props.get("instruments", [])
            record["constellation"] = props.get("constellation")
            record["mission"] = props.get("mission")
            record["gsd"] = props.get("gsd")
            record["cloud_cover"] = props.get("eo:cloud_cover")
            record["processing_level"] = props.get("processing:level")
            record["product_type"] = props.get("product_type")
            record["processing_baseline"] = props.get("processing:baseline")

            # Data provenance and federation fields
            record["data_provider"] = props.get("data_provider") or stac_item.get(
                "provider"
            )
            record["stac_api_url"] = stac_item.get(
                "stac_api_url"
            )  # Can be set by ingester
            record["license"] = stac_item.get("license") or props.get("license")

            # Viewing geometry
            record["sun_azimuth"] = props.get("view:sun_azimuth")
            record["sun_elevation"] = props.get("view:sun_elevation")
            record["view_azimuth"] = props.get("view:azimuth")
            record["view_off_nadir"] = props.get("view:off_nadir")

            # Extract asset information
            assets = stac_item.get("assets", {})
            record["asset_count"] = len(assets)
            record["asset_keys"] = list(assets.keys())[: self.config.max_asset_keys]

            # Identify COG assets
            cog_keys = []
            for key, asset in assets.items():
                if asset.get("type") == "image/tiff" or key.endswith((".tif", ".tiff")):
                    cog_keys.append(key)

            record["cog_count"] = len(cog_keys)
            record["cog_keys"] = cog_keys[: self.config.max_asset_keys]

            # Timestamps - convert string timestamps to datetime objects
            created_str = props.get("created")
            if created_str:
                try:
                    record["created"] = datetime.fromisoformat(
                        created_str.replace("Z", "+00:00")
                    )
                except (ValueError, AttributeError):
                    record["created"] = None
            else:
                record["created"] = None

            updated_str = props.get("updated")
            if updated_str:
                try:
                    record["updated"] = datetime.fromisoformat(
                        updated_str.replace("Z", "+00:00")
                    )
                except (ValueError, AttributeError):
                    record["updated"] = None
            else:
                record["updated"] = None

            return record

        except Exception as e:
            self.logger.error(
                f"Failed to flatten STAC item {stac_item.get('id', 'unknown')}: {e}"
            )
            raise

    def create_unified_record(
        self, stac_item: Any, cog_metadata: Dict[str, Any]
    ) -> Dict[str, Any]:
        """
        Create a unified record combining flattened STAC scene metadata with a single COG asset metadata.

        Args:
            stac_item: pystac.Item or STAC item dict
            cog_metadata: COG metadata dict produced by StacCogProcessor

        Returns:
            Unified record dict matching get_unified_schema()
        """
        # Accept both pystac.Item and dict
        item_dict = stac_item.to_dict() if hasattr(stac_item, "to_dict") else stac_item
        scene_record = self.flatten_stac_item(item_dict)

        # Map COG fields into unified schema names
        cog = {
            "cog_key": cog_metadata.get("asset_key"),
            "cog_href": cog_metadata.get("asset_href"),
            "cog_title": cog_metadata.get("asset_title"),
            "cog_roles": cog_metadata.get("asset_roles", []),
            "cog_width": cog_metadata.get("cog_width"),
            "cog_height": cog_metadata.get("cog_height"),
            "cog_tile_width": cog_metadata.get("cog_tile_width"),
            "cog_tile_height": cog_metadata.get("cog_tile_height"),
            "cog_dtype_code": cog_metadata.get("cog_dtype_code"),
            "cog_bits_per_sample": cog_metadata.get("cog_bits_per_sample"),
            "cog_compression_code": cog_metadata.get("cog_compression_code"),
            "cog_predictor": cog_metadata.get("cog_predictor"),
            "cog_crs_code": cog_metadata.get("cog_crs_code"),
            "cog_transform": cog_metadata.get("cog_transform"),
            "cog_tile_offsets": cog_metadata.get("cog_tile_offsets"),
            "cog_tile_byte_counts": cog_metadata.get("cog_tile_byte_counts"),
            "cog_dn_scale": cog_metadata.get("cog_dn_scale"),
            "cog_dn_offset": cog_metadata.get("cog_dn_offset"),
        }

        unified = {**scene_record, **cog}
        return unified

    def create_pyarrow_table(self, records: List[Dict[str, Any]]) -> pa.Table:
        """
        Create PyArrow table from unified records with proper schema.

        Args:
            records: List of unified record dictionaries

        Returns:
            PyArrow table with unified schema
        """
        if not records:
            # Return empty table with schema
            return pa.table([], schema=self.get_unified_schema())

        # Use GeoArrowUtils to create table with proper geometry handling
        return self.geoarrow_utils.create_geoarrow_table(
            records, self.get_unified_schema()
        )

    def validate_record(self, record: Dict[str, Any]) -> bool:
        """
        Validate a flattened record against the schema.

        Args:
            record: Flattened record dictionary

        Returns:
            True if valid, False otherwise
        """
        try:
            # Check required fields
            required_fields = ["scene_id", "collection", "datetime", "year", "month"]
            for field in required_fields:
                if field not in record or record[field] is None:
                    self.logger.warning(f"Missing required field: {field}")
                    return False

            # Validate data types
            if not isinstance(record["scene_id"], str):
                return False
            if not isinstance(record["collection"], str):
                return False
            if not isinstance(record["year"], int):
                return False
            if not isinstance(record["month"], int):
                return False

            # Validate datetime type (can be datetime object or valid ISO string)
            datetime_val = record["datetime"]
            if isinstance(datetime_val, str):
                # Try to parse the string to validate it's a valid datetime
                try:
                    datetime.fromisoformat(datetime_val.replace("Z", "+00:00"))
                except (ValueError, TypeError):
                    self.logger.warning(f"Invalid datetime string: {datetime_val}")
                    return False
            elif not isinstance(datetime_val, datetime):
                return False

            # Validate ranges
            if not (1 <= record["month"] <= 12):
                return False
            if record.get("cloud_cover") is not None:
                if not (0 <= record["cloud_cover"] <= 100):
                    return False

            return True

        except Exception as e:
            self.logger.error(f"Record validation failed: {e}")
            return False


# Legacy compatibility classes removed - use UnifiedStacSchema directly
