# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Simple and efficient date-based checking for STAC ingestion.

This module provides fast count-based comparison between STAC API and Delta Lake
to determine which dates need ingestion, replacing the complex and slow
item-by-item checking approach.
"""

import logging
from datetime import datetime, timedelta
from typing import Optional, List, Dict

import pystac_client
from deltalake import DeltaTable
import os

logger = logging.getLogger(__name__)


class SimpleDateChecker:
    """
    Fast date-based checker for STAC ingestion using count comparisons.
    
    This class provides efficient checking by comparing STAC API counts vs Delta table counts
    per day, avoiding the performance issues of item-by-item checking for large date ranges.
    """

    def __init__(
        self, table_path: str, storage_options: Optional[Dict[str, str]] = None
    ):
        """
        Initialize simple date checker.

        Args:
            table_path: Path to Delta Lake table
            storage_options: S3/cloud storage options
        """
        self.table_path = table_path
        self.storage_options = storage_options or {}
        self._delta_table_cache = None  # Cache DeltaTable instance

    def _get_delta_table_cached(self):
        """Get DeltaTable instance with caching to avoid repeated instantiation."""
        if self._delta_table_cache is not None:
            return self._delta_table_cache

        # Check if table exists and create cached instance
        if self.table_path.startswith("s3://"):
            try:
                dt = DeltaTable(self.table_path, storage_options=self.storage_options)
                self._delta_table_cache = dt
                return dt
            except Exception as e:
                if "not found" in str(e).lower() or "does not exist" in str(e).lower():
                    logger.info(f"Delta table not found: {self.table_path}")
                    return None
                else:
                    raise e
        else:
            if not os.path.exists(self.table_path):
                logger.info(f"Delta table path does not exist: {self.table_path}")
                return None
            dt = DeltaTable(self.table_path, storage_options=self.storage_options)
            self._delta_table_cache = dt
            return dt

    def _setup_duckdb_connection(self):
        """Setup DuckDB connection with Delta Lake support and S3 credentials."""
        from data_marketplace.utils.duckdb_config import setup_duckdb_with_delta_and_s3

        return setup_duckdb_with_delta_and_s3(
            memory_limit="5.5GB",
            threads=2,
            storage_options=self.storage_options,
            table_path=self.table_path
        )

    def _get_delta_count_for_date(self, date_str: str) -> int:
        """
        Get count of rows in Delta table for a specific date using DuckDB delta_scan.
        
        Args:
            date_str: Date in YYYY-MM-DD format
            
        Returns:
            Count of rows for the date, 0 if table doesn't exist or error
        """
        try:
            dt = self._get_delta_table_cached()
            if dt is None or len(dt.files()) == 0:
                return 0

            conn = self._setup_duckdb_connection()
            try:
                # Use DuckDB delta_scan for fast counting
                # Extract date from datetime column and compare
                query = f"""
                    SELECT count(*)
                    FROM delta_scan('{self.table_path}')
                    WHERE substr(datetime::VARCHAR, 1, 10) = '{date_str}'
                """
                row = conn.execute(query).fetchone()
                return int(row[0]) if row and row[0] is not None else 0
            finally:
                try:
                    conn.close()
                except Exception:
                    pass
        except Exception as e:
            logger.debug(f"Error getting Delta count for date {date_str}: {e}")
            return 0

    def _get_stac_count_for_date(
        self, catalog: pystac_client.Client, collection_id: str, date_str: str
    ) -> int:
        """
        Get count of STAC items for a specific date using catalog.search().matched().
        
        Args:
            catalog: STAC catalog client
            collection_id: STAC collection ID
            date_str: Date in YYYY-MM-DD format
            
        Returns:
            Count of STAC items for the date, 0 if error
        """
        try:
            # Create datetime range for the specific day
            start_time = f"{date_str}T00:00:00"
            end_time = f"{date_str}T23:59:59"
            datetime_range = f"{start_time}/{end_time}"
            
            # Use STAC API's fast count method
            search = catalog.search(
                collections=[collection_id],
                datetime=datetime_range
            )
            return search.matched()
        except Exception as e:
            logger.debug(f"Error getting STAC count for date {date_str}: {e}")
            return 0

    def get_dates_needing_ingestion(
        self, 
        catalog: pystac_client.Client, 
        collection_id: str, 
        datetime_range: str
    ) -> List[str]:
        """
        Get list of dates that need ingestion based on count comparison.
        
        This method compares STAC API counts vs Delta table counts per day
        and returns dates where STAC count > Delta count, indicating missing data.
        
        Args:
            catalog: STAC catalog client
            collection_id: STAC collection ID  
            datetime_range: ISO datetime range (e.g., "2024-01-01T00:00:00/2024-01-31T23:59:59")
            
        Returns:
            List of dates in YYYY-MM-DD format that need ingestion
        """
        try:
            logger.info(f"🔍 Fast count-based check for range: {datetime_range}")
            
            # Parse datetime range
            start_str, end_str = datetime_range.split("/")
            start_dt = datetime.fromisoformat(start_str.replace("Z", "+00:00"))
            end_dt = datetime.fromisoformat(end_str.replace("Z", "+00:00"))
            
            missing_dates: List[str] = []
            total_days = 0
            days_with_mismatches = 0
            
            # Check each day in the range
            current_date = start_dt
            while current_date < end_dt:
                date_str = current_date.strftime("%Y-%m-%d")
                total_days += 1
                
                # Get counts for this date
                stac_count = self._get_stac_count_for_date(catalog, collection_id, date_str)
                delta_count = self._get_delta_count_for_date(date_str)
                
                # If STAC has more items than Delta, we need to ingest this date
                if stac_count > delta_count:
                    missing_dates.append(date_str)
                    days_with_mismatches += 1
                    logger.info(
                        f"📅 Date {date_str}: STAC={stac_count}, Delta={delta_count} → needs ingestion"
                    )
                elif stac_count > 0:  # Only log if there's actually data
                    logger.debug(
                        f"📅 Date {date_str}: STAC={stac_count}, Delta={delta_count} → complete"
                    )
                
                current_date += timedelta(days=1)
            
            # Summary logging
            if missing_dates:
                logger.info(
                    f"🎯 Found {len(missing_dates)} dates needing ingestion out of {total_days} total days"
                )
                logger.info(f"📋 Missing dates: {missing_dates[:10]}{'...' if len(missing_dates) > 10 else ''}")
            else:
                logger.info(f"✅ All {total_days} dates are complete - no ingestion needed")
            
            return missing_dates
            
        except Exception as e:
            logger.warning(f"Error in fast date checking for range {datetime_range}: {e}")
            # On error, return empty list to avoid ingestion (conservative approach)
            # The caller can decide to proceed with full ingestion if needed
            return []
