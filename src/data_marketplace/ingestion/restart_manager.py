# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Smart restart logic for STAC ingestion.

This module handles date-by-date comparison between STAC API and Delta Lake
to determine optimal restart points for interrupted ingestions.
"""

import logging
from typing import Optional, List, Dict

import pystac_client
from deltalake import DeltaTable
import os

logger = logging.getLogger(__name__)


class RestartManager:
    """Manages smart restart logic for STAC ingestion."""

    def __init__(
        self, table_path: str, storage_options: Optional[Dict[str, str]] = None
    ):
        """
        Initialize restart manager.

        Args:
            table_path: Path to Delta Lake table
            storage_options: S3/cloud storage options
        """
        self.table_path = table_path
        self.storage_options = storage_options or {}
        self._prefetch_map = None  # Cache for prefetched keys
        self._delta_table_cache = None  # Cache DeltaTable instance to avoid repeated instantiation

    def _get_delta_table_cached(self):
        """Get DeltaTable instance with caching to avoid repeated instantiation."""
        if self._delta_table_cache is not None:
            return self._delta_table_cache

        # Check if table exists and create cached instance
        if self.table_path.startswith("s3://"):
            try:
                dt = DeltaTable(self.table_path, storage_options=self.storage_options)
                self._delta_table_cache = dt
                return dt
            except Exception as e:
                if "not found" in str(e).lower() or "does not exist" in str(e).lower():
                    logger.info(f"Delta table not found: {self.table_path}")
                    return None
                else:
                    raise e
        else:
            if not os.path.exists(self.table_path):
                logger.info(f"Delta table path does not exist: {self.table_path}")
                return None
            dt = DeltaTable(self.table_path, storage_options=self.storage_options)
            self._delta_table_cache = dt
            return dt

    def get_existing_cog_keys_for_scene(
        self, scene_id: str, collection: Optional[str] = None
    ) -> set:
        """Return set of existing cog_key values already ingested for a given scene (and optional collection)."""
        try:
            # Delegate to range prefetch if available (fast path)
            if hasattr(self, "_prefetch_map") and isinstance(
                getattr(self, "_prefetch_map"), dict
            ):
                return set(self._prefetch_map.get(scene_id, set()))

            # Fallback single-scene scan (legacy) - use cached DeltaTable
            dt = self._get_delta_table_cached()
            if dt is None:
                return set()

            if len(dt.files()) == 0:
                return set()

            import duckdb

            conn = duckdb.connect()
            try:
                conn.execute("INSTALL delta")
                conn.execute("LOAD delta")

                if self.table_path.startswith("s3://") and self.storage_options:
                    if "AWS_ACCESS_KEY_ID" in self.storage_options:
                        conn.execute(
                            f"""
                            CREATE SECRET s3_secret (
                                TYPE S3,
                                KEY_ID '{self.storage_options["AWS_ACCESS_KEY_ID"]}',
                                SECRET '{self.storage_options["AWS_SECRET_ACCESS_KEY"]}',
                                REGION '{self.storage_options.get("AWS_REGION", "us-west-2")}'
                            )
                        """
                        )
                    else:
                        conn.execute(
                            "CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)"
                        )

                sid = scene_id.replace("'", "''")
                where = f"scene_id = '{sid}'"
                if collection:
                    coll = collection.replace("'", "''")
                    where += f" AND collection = '{coll}'"

                query = f"""
                    SELECT DISTINCT cog_key
                    FROM delta_scan('{self.table_path}')
                    WHERE {where}
                """
                rows = conn.execute(query).fetchall()
                return {r[0] for r in rows if r and r[0] is not None}
            finally:
                try:
                    conn.close()
                except Exception:
                    pass
        except Exception as e:
            logger.warning(
                f"Failed to fetch existing cog_keys for scene_id={scene_id}: {e}"
            )
            return set()

    def prefetch_existing_cog_keys_for_range(
        self, datetime_range: str, bbox: Optional[List[float]] = None
    ) -> Dict[str, set]:
        """Prefetch existing (scene_id -> set(cog_key)) once for the entire date range."""
        logger.info(f"Prefetching existing COG keys for range: {datetime_range}")
        try:
            # Reset map
            self._prefetch_map = {}

            # Check if table exists with files
            # Use cached DeltaTable to avoid repeated instantiation
            dt = self._get_delta_table_cached()
            if dt is None:
                logger.info(f"Delta table not found: {self.table_path}")
                return self._prefetch_map

            if len(dt.files()) == 0:
                logger.info(f"Delta table has no files: {self.table_path}")
                return self._prefetch_map

            import duckdb

            conn = duckdb.connect()
            try:
                conn.execute("INSTALL delta")
                conn.execute("LOAD delta")

                if self.table_path.startswith("s3://") and self.storage_options:
                    if "AWS_ACCESS_KEY_ID" in self.storage_options:
                        conn.execute(
                            f"""
                            CREATE SECRET s3_secret (
                                TYPE S3,
                                KEY_ID '{self.storage_options["AWS_ACCESS_KEY_ID"]}',
                                SECRET '{self.storage_options["AWS_SECRET_ACCESS_KEY"]}',
                                REGION '{self.storage_options.get("AWS_REGION", "us-west-2")}'
                            )
                        """
                        )
                    else:
                        conn.execute(
                            "CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)"
                        )

                start_date_str, end_date_str = datetime_range.split("/")
                # Handle timezone issues by expanding the date range
                # STAC queries for 2025-02-02 might return data with timestamps on 2025-02-03 due to timezones
                from datetime import datetime, timedelta

                start_dt = datetime.fromisoformat(start_date_str.replace("Z", "+00:00"))
                end_dt = datetime.fromisoformat(end_date_str.replace("Z", "+00:00"))

                # Expand range by 1 day on each side to handle timezone shifts
                start_dt_expanded = start_dt - timedelta(days=1)
                end_dt_expanded = end_dt + timedelta(
                    days=2
                )  # +2 to make it inclusive and handle timezone

                where = f"datetime >= '{start_dt_expanded.isoformat()}' AND datetime < '{end_dt_expanded.isoformat()}'"

                query = f"""
                    SELECT scene_id, cog_key
                    FROM delta_scan('{self.table_path}')
                    WHERE {where}
                """
                rows = conn.execute(query).fetchall()

                # Populate in-memory map
                for sid, key in rows:
                    if sid is None or key is None:
                        continue
                    s = self._prefetch_map.get(sid)
                    if s is None:
                        s = set()
                        self._prefetch_map[sid] = s
                    s.add(key)

                logger.info(
                    f"Prefetched existing keys for range {datetime_range}: {len(self._prefetch_map)} scenes"
                )
                return self._prefetch_map
            finally:
                try:
                    conn.close()
                except Exception:
                    pass
        except Exception as e:
            logger.warning(f"Failed prefetch for range {datetime_range}: {e}")
            self._prefetch_map = {}
            return self._prefetch_map

    # Backward-compatible helper expected by some unit tests
    def get_delta_count_for_date(self, date_str: str) -> int:
        """Return approximate row count for a given date (YYYY-MM-DD) from the unified table.
        This is a simplified helper retained for test compatibility.
        """
        try:
            # Use cached DeltaTable to avoid repeated instantiation
            dt = self._get_delta_table_cached()
            if dt is None or len(dt.files()) == 0:
                return 0
            import duckdb

            conn = duckdb.connect()
            try:
                conn.execute("INSTALL delta")
                conn.execute("LOAD delta")
                if self.table_path.startswith("s3://") and self.storage_options:
                    if "AWS_ACCESS_KEY_ID" in self.storage_options:
                        conn.execute(
                            f"""
                            CREATE SECRET s3_secret (
                                TYPE S3,
                                KEY_ID '{self.storage_options["AWS_ACCESS_KEY_ID"]}',
                                SECRET '{self.storage_options["AWS_SECRET_ACCESS_KEY"]}',
                                REGION '{self.storage_options.get("AWS_REGION", "us-west-2")}'
                            )
                        """
                        )
                    else:
                        conn.execute(
                            "CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)"
                        )
                q = f"""
                    SELECT count(*)
                    FROM delta_scan('{self.table_path}')
                    WHERE substr(datetime::VARCHAR, 1, 10) = '{date_str}'
                """
                row = conn.execute(q).fetchone()
                return int(row[0]) if row and row[0] is not None else 0
            finally:
                try:
                    conn.close()
                except Exception:
                    pass
        except Exception:
            return 0

    # Backward-compatible method used by unit tests; superseded by key-level checks
    def find_missing_dates(
        self, catalog, collection_id: str, datetime_range: str
    ) -> List[str]:
        """Return list of YYYY-MM-DD dates where STAC count > Delta count.
        Uses catalog.search(...).matched() on day slices.
        """
        try:
            from datetime import datetime, timedelta

            start_str, end_str = datetime_range.split("/")
            start_dt = datetime.fromisoformat(start_str.replace("Z", "+00:00"))
            end_dt = datetime.fromisoformat(end_str.replace("Z", "+00:00"))
            missing: List[str] = []
            cur = start_dt
            while cur < end_dt:
                next_day = cur + timedelta(days=1)
                range_str = f"{cur.strftime('%Y-%m-%dT00:00:00')}/{next_day.strftime('%Y-%m-%dT00:00:00')}"
                # STAC count via lightweight matched()
                try:
                    stac_count = catalog.search(
                        collections=[collection_id], datetime=range_str
                    ).matched()
                except Exception:
                    stac_count = 0
                # Delta count via helper
                delta_count = self.get_delta_count_for_date(cur.strftime("%Y-%m-%d"))
                if stac_count > delta_count:
                    missing.append(cur.strftime("%Y-%m-%d"))
                cur = next_day
            return missing
        except Exception as e:
            logger.debug(f"find_missing_dates failed: {e}")
            return []

    def check_missing_cog_keys_for_range(
        self,
        catalog: pystac_client.Client,
        collection_id: str,
        datetime_range: str,
        bbox: Optional[List[float]] = None,
        max_items: Optional[int] = None,
    ) -> tuple[bool, Optional[List]]:
        """
        Check if there are any missing COG keys in the date range.

        CRITICAL: This method checks EVERY STAC item and EVERY COG asset to ensure
        complete data integrity. No sampling - we must ingest every missing COG.

        Returns (needs_ingestion: bool, missing_scenes: Optional[List]) where:
        - needs_ingestion: True if there are missing keys, False if all keys exist
        - missing_scenes: List of STAC items that have missing COGs (None if no missing keys)
        """
        try:
            logger.info(f"Checking COG key completeness for range: {datetime_range}")

            # First, prefetch existing keys for the range using DuckDB (this will be reused later)
            existing_keys_map = self.prefetch_existing_cog_keys_for_range(
                datetime_range, bbox
            )

            # If no existing keys found, we definitely need ingestion (return all items)
            if not existing_keys_map:
                logger.info(
                    f"No existing keys found for range {datetime_range} - ingestion needed"
                )
                # Return True with None to indicate we need all items (will use normal STAC search)
                return True, None

            # Get ALL STAC items for the range - NO LIMIT, NO SAMPLING
            search_params = {
                "collections": [collection_id],
                "datetime": datetime_range,
                # NO LIMIT - we must check every single item
            }
            if bbox:
                search_params["bbox"] = bbox

            search = catalog.search(**search_params)

            # Check EVERY item in the date range for missing COG keys
            items_checked = 0
            total_expected_cogs = 0
            total_existing_cogs = 0
            missing_scenes = []  # Collect scenes with missing COGs

            for item in search.items():
                items_checked += 1

                # Respect max_items limit during complete check
                if max_items is not None and items_checked > max_items:
                    logger.info(f"Reached max_items limit ({max_items}) during complete check")
                    break

                scene_id = getattr(item, "id", None)
                if not scene_id:
                    continue

                # Get ALL COG assets from this item using proper COG detection
                expected_cog_keys = set()
                for key, asset in getattr(item, "assets", {}).items():
                    # Use proper COG asset detection (same logic as processor)
                    if self._is_cog_asset_simple(asset):
                        expected_cog_keys.add(key)

                total_expected_cogs += len(expected_cog_keys)

                # Check if ALL COG keys for this scene exist in Delta table
                existing_keys = existing_keys_map.get(scene_id, set())
                existing_cog_keys = expected_cog_keys & existing_keys  # Intersection
                total_existing_cogs += len(existing_cog_keys)

                missing_keys = expected_cog_keys - existing_keys

                if missing_keys:
                    logger.info(
                        f"Found missing COG keys for scene {scene_id}: {missing_keys} "
                        f"(checked {items_checked} items, {total_existing_cogs}/{total_expected_cogs} COGs exist)"
                    )
                    missing_scenes.append(scene_id)

                # Log progress for large ranges
                if items_checked % 1000 == 0:
                    logger.info(f"Checked {items_checked} items, {total_existing_cogs}/{total_expected_cogs} COGs exist")

            # Return results based on what we found
            if missing_scenes:
                logger.info(
                    f"🎯 Found {len(missing_scenes)} scenes with missing COGs: {missing_scenes[:5]}{'...' if len(missing_scenes) > 5 else ''}"
                )
                return True, missing_scenes
            else:
                logger.info(
                    f"✅ Complete check: ALL COG keys exist for range {datetime_range} "
                    f"(checked {items_checked} items, {total_existing_cogs}/{total_expected_cogs} COGs complete) - skipping ingestion"
                )
                return False, None

        except Exception as e:
            logger.warning(
                f"Error checking COG key completeness for range {datetime_range}: {e}"
            )
            # If we can't determine due to STAC API issues, proceed with ingestion
            # The per-item filtering will handle the actual deduplication
            logger.info("Falling back to per-item filtering due to pre-check error")
            return True

    def _is_cog_asset_simple(self, asset) -> bool:
        """
        Simple COG asset detection without importing full processor.

        Uses same logic as StacCogProcessor.is_cog_asset() for consistency.
        """
        if not getattr(asset, "href", None):
            return False

        href = str(asset.href).lower()
        media_type = str(getattr(asset, "media_type", "")).lower()

        # Accept HTTP(S) GeoTIFF/COG assets and ignore JP2
        if not (href.startswith("http://") or href.startswith("https://")):
            return False

        # Accept common GeoTIFF media types; tolerate missing media_type if extension matches
        geotiff_types = {
            "image/tiff",
            "image/tiff; application=geotiff",
            "image/tiff; profile=cloud-optimized",
            "image/geotiff",
        }
        if (media_type and media_type not in geotiff_types) and not href.endswith(
            (".tif", ".tiff")
        ):
            return False

        # Exclude JP2 QA assets explicitly
        if href.endswith(".jp2") or media_type == "image/jp2":
            return False

        return True
