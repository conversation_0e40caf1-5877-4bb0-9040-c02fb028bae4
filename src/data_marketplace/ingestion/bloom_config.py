# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Bloom filter configuration for enhanced query performance."""

import logging
from typing import List, Dict, Any, Optional
from data_marketplace.config.settings import Settings

logger = logging.getLogger(__name__)


class BloomFilterConfig:
    """Configuration and utilities for Bloom filters in Parquet files."""

    def __init__(self, settings: Optional[Settings] = None):
        """
        Initialize Bloom filter configuration.

        Args:
            settings: Settings instance (uses global if None)
        """
        if settings is None:
            from data_marketplace.config.settings import settings as global_settings

            settings = global_settings

        self.settings = settings
        self.logger = logger

    def get_bloom_filter_columns(self) -> List[str]:
        """
        Get list of columns that should have Bloom filters.

        Returns:
            List of column names
        """
        return self.settings.parquet.bloom_filter_columns

    def should_enable_bloom_filters(self) -> bool:
        """
        Check if Bloom filters should be enabled.

        Returns:
            True if Bloom filters should be enabled
        """
        return self.settings.parquet.enable_bloom_filters

    def get_parquet_write_options(self) -> Dict[str, Any]:
        """
        Get Parquet write options with optimized configuration for STAC metadata.

        Returns:
            Dictionary of Parquet write options
        """
        options = {
            "compression": self.settings.parquet.compression,
            "row_group_size": self.settings.parquet.row_group_size,
            "write_statistics": True,
            "use_dictionary": self.settings.parquet.use_dictionary,
        }

        # Only add compression_level if it's set (not needed for snappy)
        if self.settings.parquet.compression_level is not None:
            options["compression_level"] = self.settings.parquet.compression_level

        return options

    def get_delta_write_options(self, schema_columns: List[str]) -> Dict[str, Any]:
        """
        Get Delta Lake write options with Bloom filter support for write_deltalake().

        Args:
            schema_columns: List of column names in the schema

        Returns:
            Dictionary of options for write_deltalake()
        """
        options = {}

        if self.should_enable_bloom_filters():
            # Validate columns exist in schema
            valid_bloom_columns = self.validate_bloom_filter_columns(schema_columns)

            if valid_bloom_columns:
                # PyArrow 17+ supports bloom_filter_columns in write_deltalake
                options["bloom_filter_columns"] = valid_bloom_columns
                self.logger.info(
                    f"Enabled Bloom filters for columns: {valid_bloom_columns}"
                )
            else:
                self.logger.warning("No valid Bloom filter columns found in schema")

        return options

    def estimate_bloom_filter_size(
        self, num_rows: int, false_positive_rate: float = 0.01
    ) -> int:
        """
        Estimate Bloom filter size in bytes.

        Args:
            num_rows: Number of rows in the dataset
            false_positive_rate: Desired false positive rate

        Returns:
            Estimated size in bytes
        """
        import math

        # Bloom filter size calculation
        # m = -(n * ln(p)) / (ln(2)^2)
        # where m = number of bits, n = number of items, p = false positive rate

        num_bits = -(num_rows * math.log(false_positive_rate)) / (math.log(2) ** 2)
        num_bytes = math.ceil(num_bits / 8)

        # Multiply by number of columns with Bloom filters
        num_columns = len(self.get_bloom_filter_columns())
        total_bytes = num_bytes * num_columns

        return int(total_bytes)

    def get_optimal_hash_functions(self, false_positive_rate: float = 0.01) -> int:
        """
        Calculate optimal number of hash functions for Bloom filter.

        Args:
            false_positive_rate: Desired false positive rate

        Returns:
            Optimal number of hash functions
        """
        import math

        # k = (m/n) * ln(2)
        # For simplicity, use the approximation: k = -log2(p)
        k = -math.log2(false_positive_rate)
        return max(1, int(round(k)))

    def validate_bloom_filter_columns(self, schema_columns: List[str]) -> List[str]:
        """
        Validate that Bloom filter columns exist in the schema.

        Args:
            schema_columns: List of column names in the schema

        Returns:
            List of valid Bloom filter columns
        """
        bloom_columns = self.get_bloom_filter_columns()
        valid_columns = []

        for column in bloom_columns:
            if column in schema_columns:
                valid_columns.append(column)
            else:
                self.logger.warning(
                    f"Bloom filter column '{column}' not found in schema"
                )

        return valid_columns
