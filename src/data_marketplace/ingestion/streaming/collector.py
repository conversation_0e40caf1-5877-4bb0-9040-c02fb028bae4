# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Collector component for streaming STAC processing."""

import time
import logging
from typing import Dict, Any, List, Tuple, AsyncIterator
from anyio.streams.memory import MemoryObjectReceiveStream

from data_marketplace.ingestion.streaming.types import StreamingStats

logger = logging.getLogger(__name__)


class StreamingCollector:
    """
    Collector component that batches unified records for output.

    This component handles:
    - Collecting processed records from consumers
    - Batching records for efficient output
    - Creating batch statistics for compatibility
    - Managing output flow control
    """

    def __init__(self, stats: StreamingStats):
        self.stats = stats

    async def collect(
        self,
        receive: MemoryObjectReceiveStream,
        batch_size: int,
    ) -> AsyncIterator[Tuple[Dict[str, Any], List[Dict[str, Any]]]]:
        """
        Collect processed records into batches for output.

        Args:
            receive: Stream to receive processed records from consumers
            batch_size: Number of records per batch

        Yields:
            Tuple of (batch_stats, unified_records)
        """
        logger.debug("Starting output collector")
        async with receive:
            batch_records = []
            batch_start_time = time.time()

            async for record in receive:
                batch_records.append(record)

                if len(batch_records) >= batch_size:
                    # Yield complete batch
                    batch_stats = self._create_batch_stats(
                        batch_records, batch_start_time
                    )
                    yield batch_stats, batch_records

                    # Reset for next batch
                    batch_records = []
                    batch_start_time = time.time()

            # Yield remaining records if any
            if batch_records:
                batch_stats = self._create_batch_stats(batch_records, batch_start_time)
                yield batch_stats, batch_records

        logger.debug("Output collector finished")

    def _create_batch_stats(
        self, batch_records: List[Dict[str, Any]], batch_start_time: float
    ) -> Dict[str, Any]:
        """
        Create batch statistics compatible with traditional processor format.

        Args:
            batch_records: List of records in this batch
            batch_start_time: When this batch started

        Returns:
            Dictionary of batch statistics
        """
        batch_time = time.time() - batch_start_time
        records_per_second = len(batch_records) / batch_time if batch_time > 0 else 0

        # Create statistics compatible with existing code
        batch_stats = {
            "stac_items_processed": len(
                batch_records
            ),  # Assume 1:1 mapping for compatibility
            "cog_assets_processed": len(
                batch_records
            ),  # Each record represents one COG asset
            "unified_records_written": len(batch_records),
            "unified_records_created": len(batch_records),  # For compatibility
            "batch_time": batch_time,
            "records_per_second": records_per_second,
            "errors": [],  # Compatibility with existing error handling
        }

        return batch_stats

    def get_stats_summary(self) -> Dict[str, Any]:
        """Get a summary of collection statistics."""
        return {
            "total_records_collected": self.stats.records_created,
            "total_items_processed": self.stats.items_processed,
            "total_errors": self.stats.errors,
            "elapsed_time": self.stats.elapsed_time,
            "records_per_second": self.stats.records_per_second,
            "items_per_second": self.stats.items_per_second,
        }
