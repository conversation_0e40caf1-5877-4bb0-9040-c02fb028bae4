# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Monitoring and statistics for streaming STAC processing."""

import asyncio
import logging
from typing import Optional
from data_marketplace.ingestion.streaming.types import StreamingStats

logger = logging.getLogger(__name__)


class StreamingMonitor:
    """Real-time monitoring for streaming processor."""

    def __init__(self, stats: StreamingStats, interval: float = 5.0):
        """
        Initialize monitor.

        Args:
            stats: StreamingStats instance to monitor
            interval: Monitoring interval in seconds
        """
        self.stats = stats
        self.interval = interval
        self._running = False
        self._task: Optional[asyncio.Task] = None

    async def start(self):
        """Start monitoring task."""
        if self._running:
            return

        self._running = True
        self._task = asyncio.create_task(self._monitor_loop())
        logger.info(f"Started streaming monitor (interval: {self.interval}s)")

    async def stop(self):
        """Stop monitoring task."""
        if not self._running:
            return

        self._running = False
        if self._task:
            self._task.cancel()
            try:
                await self._task
            except asyncio.CancelledError:
                pass

        logger.info("Stopped streaming monitor")

    async def _monitor_loop(self):
        """Main monitoring loop."""
        try:
            while self._running:
                await asyncio.sleep(self.interval)
                if self._running:  # Check again after sleep
                    self._log_stats()
        except asyncio.CancelledError:
            pass

    def _log_stats(self):
        """Log current statistics."""
        stats_dict = self.stats.to_dict()
        logger.info(
            f"📊 Streaming Stats: "
            f"{stats_dict['items_processed']} items, "
            f"{stats_dict['records_created']} records, "
            f"{stats_dict['items_per_second']:.1f} items/sec, "
            f"{stats_dict['records_per_second']:.1f} records/sec"
        )

        if stats_dict["errors"] > 0:
            logger.warning(f"⚠️ Errors encountered: {stats_dict['errors']}")

    async def __aenter__(self):
        """Async context manager entry."""
        await self.start()
        return self

    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        # Unused parameters are required by async context manager protocol
        await self.stop()
