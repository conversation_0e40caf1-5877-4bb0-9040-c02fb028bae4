# SPDX-FileCopyrightText: Terrafloww Labs, 2025
"""Consumer component for streaming STAC processing."""

import asyncio
import logging
from typing import Dict, Any
from anyio.streams.memory import MemoryObjectReceiveStream, MemoryObjectSendStream

from .types import StreamingStats, CogWorkItem

logger = logging.getLogger(__name__)


class StreamingConsumer:
    """
    Consumer component that processes unified records.
    
    In the current simplified architecture, consumers just pass through
    the unified records since the producer already does all the heavy lifting.
    This maintains the producer-consumer pattern for potential future enhancements.
    """
    
    def __init__(self, consumer_id: int, stats: StreamingStats):
        self.consumer_id = consumer_id
        self.stats = stats
    
    async def consume(
        self,
        receive: MemoryObjectReceiveStream,
        send: MemoryObjectSendStream,
        cog_processor,
        use_fast_parser: bool = True,
        batch_size: int = 16,  # Batch size for async-tiff optimization
    ):
        """
        Consume COG work items and emit unified records.

        Uses batch processing for async-tiff to achieve 5x performance improvement.

        Args:
            receive: Stream to receive CogWorkItems from
            send: Stream to send unified records to
            cog_processor: StacCogProcessor to parse COG headers
            use_fast_parser: Whether to use the fast parser
            batch_size: Number of COG URLs to batch together for async-tiff
        """
        logger.debug(f"Consumer {self.consumer_id} starting with batch_size={batch_size}")

        try:
            async with receive, send:
                # Check if we can use batch processing with async-tiff
                use_batch_processing = (
                    hasattr(cog_processor, 'parser') and
                    hasattr(cog_processor.parser, 'parse_batch')
                )

                if use_batch_processing:
                    await self._consume_with_batching(receive, send, cog_processor, batch_size)
                else:
                    await self._consume_individual(receive, send, cog_processor, use_fast_parser)

        except Exception as e:
            logger.error(f"Consumer {self.consumer_id} fatal error: {e}")

        logger.debug(f"Consumer {self.consumer_id} finished")

    async def _consume_with_batching(
        self, receive, send, cog_processor, batch_size: int
    ):
        """Consume with batch processing for 5x async-tiff performance."""
        work_items_batch = []

        async for work_item in receive:
            try:
                if not isinstance(work_item, CogWorkItem):
                    # Pass through legacy records if any
                    await send.send(work_item)
                    continue

                work_items_batch.append(work_item)

                # Process batch when full or stream ends
                if len(work_items_batch) >= batch_size:
                    await self._process_batch(work_items_batch, send, cog_processor)
                    work_items_batch = []

            except Exception as e:
                logger.error(f"Consumer {self.consumer_id} error processing work item: {e}")
                self.stats.update(errors=1)

        # Process remaining items in final batch
        if work_items_batch:
            await self._process_batch(work_items_batch, send, cog_processor)

    async def _process_batch(self, work_items_batch, send, cog_processor):
        """Process a batch of work items using async-tiff batch processing."""
        try:
            # Extract URLs for batch processing
            urls = [item.asset_href for item in work_items_batch]

            # Use async-tiff batch processing for 5x speedup
            results = await cog_processor.parser.parse_batch(urls)

            # Process results and emit unified records
            for work_item, result in zip(work_items_batch, results):
                if result:
                    # Convert to format expected by Delta Lake pipeline
                    cog_meta = result.to_dict()

                    # Extract DN scale/offset from STAC metadata if available
                    if hasattr(cog_processor, 'extract_dn_scale_offset_from_asset'):
                        stac_dn_scale, stac_dn_offset = cog_processor.extract_dn_scale_offset_from_asset(
                            work_item.asset_data
                        )

                        # Use STAC values if COG headers don't have them
                        if cog_meta.get("cog_dn_scale") is None and stac_dn_scale is not None:
                            cog_meta["cog_dn_scale"] = stac_dn_scale

                        if cog_meta.get("cog_dn_offset") is None and stac_dn_offset is not None:
                            cog_meta["cog_dn_offset"] = stac_dn_offset

                    cog_record = {
                        "asset_key": work_item.asset_key,
                        "asset_href": work_item.asset_href,
                        "cog_key": work_item.asset_key,
                        "cog_href": work_item.asset_href,
                        "asset_media_type": work_item.asset_data.get("type"),
                        "asset_roles": work_item.asset_data.get("roles", []),
                        "asset_title": work_item.asset_data.get("title"),
                        "asset_description": work_item.asset_data.get("description"),
                        **cog_meta,
                    }

                    unified_record = {**work_item.base_scene, **cog_record}
                    await send.send(unified_record)
                    self.stats.update(records=1)
                else:
                    logger.debug(f"Failed to parse COG header for {work_item.asset_href}")
                    self.stats.update(errors=1)

        except Exception as e:
            logger.error(f"Consumer {self.consumer_id} error processing batch: {e}")
            self.stats.update(errors=len(work_items_batch))

    async def _consume_individual(self, receive, send, cog_processor, use_fast_parser: bool):
        """Fallback to individual processing for non-async-tiff processors."""
        async for work_item in receive:
            try:
                if not isinstance(work_item, CogWorkItem):
                    # Pass through legacy records if any
                    await send.send(work_item)
                    continue

                # Parse COG header for this asset (individual processing)
                if use_fast_parser and hasattr(cog_processor, 'parse_cog_headers_for_asset_fast'):
                    cog_records = await cog_processor.parse_cog_headers_for_asset_fast(
                        work_item.asset_href, work_item.asset_key, work_item.asset_data
                    )
                else:
                    cog_records = await cog_processor.parse_cog_headers_for_asset(
                        work_item.asset_href, work_item.asset_key, work_item.asset_data
                    )

                # Emit unified records combining base scene and cog record
                for cog_record in cog_records:
                    unified_record = {**work_item.base_scene, **cog_record}
                    await send.send(unified_record)
                    self.stats.update(records=1)

            except Exception as e:
                logger.error(f"Consumer {self.consumer_id} error processing work item: {e}")
                self.stats.update(errors=1)

    async def process_record(self, unified_record: Dict[str, Any]) -> Dict[str, Any]:
        """
        Process a single unified record.
        
        This method can be extended in the future for additional processing
        like validation, transformation, or enrichment.
        
        Args:
            unified_record: The unified record to process
            
        Returns:
            The processed unified record
        """
        # Currently just pass through, but this is where we could add:
        # - Record validation
        # - Data transformation
        # - Additional enrichment
        # - Quality checks
        
        return unified_record
