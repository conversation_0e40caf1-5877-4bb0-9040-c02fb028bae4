# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""DuckDB configuration utilities for memory-constrained environments."""

import logging
from typing import Optional

logger = logging.getLogger(__name__)


def configure_duckdb_memory(
    conn,
    memory_limit: str = "5.5GB",
    threads: int = 2,
    preserve_insertion_order: bool = False
) -> None:
    """
    Configure DuckDB connection for memory-constrained environments.
    
    This function applies memory optimization settings that are suitable
    for VMs with limited RAM (e.g., 7GB total).
    
    Args:
        conn: DuckDB connection object
        memory_limit: Maximum memory DuckDB can use (default: 5.5GB for 7GB VM)
        threads: Number of threads to use (default: 2 to save memory)
        preserve_insertion_order: Whether to preserve insertion order (default: False to save memory)
    """
    try:
        conn.execute(f"SET memory_limit='{memory_limit}'")
        conn.execute(f"SET threads={threads}")
        conn.execute(f"SET preserve_insertion_order={preserve_insertion_order}")
        
        logger.debug(f"DuckDB configured: memory_limit={memory_limit}, threads={threads}, preserve_insertion_order={preserve_insertion_order}")
        
    except Exception as e:
        logger.warning(f"Failed to configure DuckDB memory settings: {e}")


def setup_duckdb_with_delta_and_s3(
    memory_limit: str = "5.5GB",
    threads: int = 2,
    storage_options: Optional[dict] = None,
    table_path: Optional[str] = None
):
    """
    Create a DuckDB connection with Delta Lake and S3 support, optimized for memory-constrained environments.
    
    Args:
        memory_limit: Maximum memory DuckDB can use
        threads: Number of threads to use
        storage_options: S3 storage options (AWS credentials, region, etc.)
        table_path: Table path to determine if S3 configuration is needed
        
    Returns:
        Configured DuckDB connection
    """
    import duckdb
    
    conn = duckdb.connect()
    
    # Install and load required extensions
    conn.execute("INSTALL delta")
    conn.execute("LOAD delta")
    
    # Configure memory settings
    configure_duckdb_memory(conn, memory_limit, threads, preserve_insertion_order=False)
    
    # Configure S3 if needed
    if table_path and table_path.startswith("s3://") and storage_options:
        if "AWS_ACCESS_KEY_ID" in storage_options:
            conn.execute(f"""
                CREATE SECRET s3_secret (
                    TYPE S3,
                    KEY_ID '{storage_options["AWS_ACCESS_KEY_ID"]}',
                    SECRET '{storage_options["AWS_SECRET_ACCESS_KEY"]}',
                    REGION '{storage_options.get("AWS_REGION", "us-west-2")}'
                )
            """)
        else:
            conn.execute("CREATE SECRET s3_secret (TYPE S3, PROVIDER credential_chain)")
    
    return conn
