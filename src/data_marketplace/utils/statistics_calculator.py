# SPDX-FileCopyrightText: Terrafloww Labs, 2025

"""
Statistics calculation utility for STAC ingestion performance metrics.

This module provides reusable utilities for calculating and formatting
performance statistics across different ingestion methods.
"""

import logging
from datetime import datetime
from typing import Dict, Any, Optional, List

logger = logging.getLogger(__name__)


class StatisticsCalculator:
    """
    Utility class for calculating and formatting ingestion performance statistics.
    
    Provides consistent methods for:
    - Duration calculation
    - Performance metrics (items/sec, records/sec)
    - Statistics initialization and updates
    - Performance logging
    """

    @staticmethod
    def initialize_stats(
        collection_id: str,
        start_time: datetime,
        additional_fields: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Initialize a standard statistics dictionary.
        
        Args:
            collection_id: STAC collection identifier
            start_time: Processing start time
            additional_fields: Optional additional fields to include
            
        Returns:
            Initialized statistics dictionary
        """
        stats = {
            "collection_id": collection_id,
            "start_time": start_time.isoformat(),
            "stac_items_processed": 0,
            "cog_assets_processed": 0,
            "unified_records_written": 0,
            "errors": [],
            "performance": {},
        }
        
        if additional_fields:
            stats.update(additional_fields)
            
        return stats

    @staticmethod
    def calculate_duration_stats(
        stats: Dict[str, Any],
        start_time: datetime,
        end_time: Optional[datetime] = None
    ) -> Dict[str, Any]:
        """
        Calculate duration and performance metrics for statistics.
        
        Args:
            stats: Statistics dictionary to update
            start_time: Processing start time
            end_time: Processing end time (defaults to now)
            
        Returns:
            Updated statistics dictionary with duration and performance metrics
        """
        if end_time is None:
            end_time = datetime.now()
            
        duration = (end_time - start_time).total_seconds()
        
        # Update basic timing fields
        stats.update({
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
        })
        
        # Calculate performance metrics
        performance = {}
        
        if duration > 0:
            performance["items_per_second"] = stats["stac_items_processed"] / duration
            performance["unified_records_per_second"] = stats["unified_records_written"] / duration
            
            # Include COG assets per second if available
            if "cog_assets_processed" in stats and stats["cog_assets_processed"] > 0:
                performance["cog_assets_per_second"] = stats["cog_assets_processed"] / duration
        else:
            performance["items_per_second"] = 0.0
            performance["unified_records_per_second"] = 0.0
            if "cog_assets_processed" in stats:
                performance["cog_assets_per_second"] = 0.0
        
        # Add total records for convenience
        performance["total_records_written"] = stats["unified_records_written"]
        
        stats["performance"] = performance
        return stats

    @staticmethod
    def create_empty_stats(
        collection_id: str,
        start_time: datetime,
        message: str = "No processing needed"
    ) -> Dict[str, Any]:
        """
        Create statistics for cases where no processing was performed.
        
        Args:
            collection_id: STAC collection identifier
            start_time: Processing start time
            message: Descriptive message for why no processing occurred
            
        Returns:
            Statistics dictionary with zero values and message
        """
        end_time = datetime.now()
        duration = (end_time - start_time).total_seconds()
        
        return {
            "collection_id": collection_id,
            "start_time": start_time.isoformat(),
            "end_time": end_time.isoformat(),
            "duration_seconds": duration,
            "stac_items_processed": 0,
            "unified_records_written": 0,
            "performance": {
                "items_per_second": 0.0,
                "unified_records_per_second": 0.0,
                "total_records_written": 0,
            },
            "message": message,
        }

    @staticmethod
    def log_performance_summary(stats: Dict[str, Any], operation_name: str = "ingestion") -> None:
        """
        Log a standardized performance summary.
        
        Args:
            stats: Statistics dictionary containing performance metrics
            operation_name: Name of the operation for logging context
        """
        duration = stats.get("duration_seconds", 0)
        performance = stats.get("performance", {})
        
        logger.info(f"Unified STAC {operation_name} completed in {duration:.2f}s")
        
        items_per_sec = performance.get("items_per_second", 0)
        logger.info(f"Performance: {items_per_sec:.2f} items/sec")
        
        total_records = performance.get("total_records_written", 0)
        logger.info(f"Total unified records written: {total_records}")

    @staticmethod
    def initialize_combined_stats(
        operation_count: int,
        operation_type: str = "dates"
    ) -> Dict[str, Any]:
        """
        Initialize statistics for combined/batch operations.
        
        Args:
            operation_count: Number of operations to be performed
            operation_type: Type of operations (e.g., "dates", "batches")
            
        Returns:
            Initialized combined statistics dictionary
        """
        return {
            "stac_items_processed": 0,
            "unified_records_written": 0,
            "duration_seconds": 0.0,
            "errors": [],
            "performance": {
                "items_per_second": 0.0,
                "unified_records_per_second": 0.0,
            },
            f"{operation_type}_processed": [],
            "message": f"Targeted ingestion of {operation_count} {operation_type}"
        }

    @staticmethod
    def update_combined_stats_message(
        stats: Dict[str, Any],
        successful_count: int,
        total_count: int,
        failed_items: Optional[List[str]] = None
    ) -> Dict[str, Any]:
        """
        Update the message field in combined statistics with results summary.
        
        Args:
            stats: Statistics dictionary to update
            successful_count: Number of successful operations
            total_count: Total number of operations attempted
            failed_items: Optional list of failed items
            
        Returns:
            Updated statistics dictionary
        """
        if failed_items:
            stats["message"] += f" - {successful_count}/{total_count} successful, {len(failed_items)} failed"
            stats["failed_items"] = failed_items
        else:
            stats["message"] += f" - all {successful_count} successful"
            
        return stats

    @staticmethod
    def add_table_row_count(stats: Dict[str, Any], total_table_rows: int) -> Dict[str, Any]:
        """
        Add total table row count to performance statistics.
        
        Args:
            stats: Statistics dictionary to update
            total_table_rows: Total number of rows in the table
            
        Returns:
            Updated statistics dictionary
        """
        if total_table_rows > 0:
            logger.info(f"📊 Total table rows after ingestion: {total_table_rows:,}")
            stats["performance"]["total_table_rows"] = total_table_rows
            
        return stats
