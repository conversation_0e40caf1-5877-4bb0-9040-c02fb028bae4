# Scale Clarification: Spatial vs Radiometric

## ⚠️ CRITICAL DISTINCTION

There are **TWO COMPLETELY DIFFERENT** types of "scale" in geospatial data:

### 1. Spatial Scale (Resolution) - FROM COG HEADERS
- **Source**: COG headers `model_pixel_scale` (TIFF tag 33550)
- **Purpose**: Geographic/spatial resolution 
- **Units**: Meters per pixel, degrees per pixel, etc.
- **Example**: `10.0` = 10 meters per pixel
- **Storage**: Part of `cog_transform` array
- **Use**: Geospatial calculations, reprojection, spatial analysis

### 2. Radiometric Scale (Calibration) - FROM STAC METADATA
- **Source**: STAC metadata `raster:bands[0].scale` or asset `scale`
- **Purpose**: Pixel value calibration/conversion
- **Units**: Unitless scaling factor
- **Example**: `0.001` = multiply raw values by 0.001
- **Storage**: `cog_dn_scale` field
- **Use**: Converting raw pixel values to physical units (reflectance, radiance, etc.)

## Examples

### Spatial Scale (COG Headers)
```json
// From COG ModelPixelScaleTag
"model_pixel_scale": [10.0, 10.0, 0.0]
// Means: 10 meters per pixel in X and Y directions

// Stored in Delta Lake as part of transform:
"cog_transform": [10.0, 0.0, 0.0, -10.0, 300000.0, 4100040.0]
```

### Radiometric Scale (STAC Metadata)
```json
// From STAC raster:bands extension
"raster:bands": [
  {
    "scale": 0.001,
    "offset": 0,
    "unit": "1"
  }
]

// Or directly in asset:
{
  "scale": 0.001,
  "offset": 0
}

// Stored in Delta Lake as:
"cog_dn_scale": 0.001,
"cog_dn_offset": 0
```

## Usage

### Spatial Scale Usage
```python
# Get pixel size in meters
pixel_size_x = cog_transform[0]  # 10.0 meters
pixel_size_y = abs(cog_transform[3])  # 10.0 meters

# Calculate area of one pixel
pixel_area = pixel_size_x * pixel_size_y  # 100 square meters
```

### Radiometric Scale Usage
```python
# Convert raw pixel values to calibrated values
raw_value = 1500  # Raw pixel value from COG
calibrated_value = cog_dn_scale * raw_value + cog_dn_offset
# calibrated_value = 0.001 * 1500 + 0 = 1.5 (reflectance)
```

## Why This Matters

### Wrong Approach (What We Fixed)
```python
# ❌ WRONG: Using spatial scale for radiometric calibration
pixel_scale = 10.0  # This is meters/pixel, not radiometric!
calibrated = pixel_scale * raw_value  # Makes no sense!
```

### Correct Approach
```python
# ✅ CORRECT: Use appropriate scale for each purpose

# Spatial calculations
pixel_size = cog_transform[0]  # 10.0 meters/pixel
area = pixel_size * pixel_size  # 100 m²

# Radiometric calibration  
if cog_dn_scale is not None:
    calibrated = cog_dn_scale * raw_value + (cog_dn_offset or 0)
else:
    calibrated = raw_value  # No calibration available
```

## Field Mapping Summary

| Purpose | Source | Delta Lake Field | Example Value | Units |
|---------|--------|------------------|---------------|-------|
| **Spatial Resolution** | COG headers | `cog_transform[0]` | 10.0 | meters/pixel |
| **Radiometric Calibration** | STAC metadata | `cog_dn_scale` | 0.001 | unitless |
| **Radiometric Offset** | STAC metadata | `cog_dn_offset` | 0 | same as output |

## Common STAC Examples

### Sentinel-2 (Reflectance)
```json
"raster:bands": [
  {
    "scale": 0.0001,
    "offset": 0,
    "unit": "1"
  }
]
// Converts raw values to reflectance (0-1)
```

### Landsat (Radiance)
```json
"raster:bands": [
  {
    "scale": 0.00341802,
    "offset": 0.1,
    "unit": "W/(m²·sr·μm)"
  }
]
// Converts raw values to radiance
```

### Temperature Data
```json
"raster:bands": [
  {
    "scale": 0.02,
    "offset": 273.15,
    "unit": "K"
  }
]
// Converts raw values to Kelvin
```

## Implementation Notes

### COG Headers (Spatial)
- Always present in georeferenced COGs
- Extracted by async-tiff from TIFF tags
- Used for geospatial transformations
- Stored in `cog_transform` array

### STAC Metadata (Radiometric)
- Optional - may not be present
- Extracted from STAC `raster:bands` or asset properties
- Used for pixel value calibration
- Stored in `cog_dn_scale` and `cog_dn_offset`
- NULL when not specified (never default values)

## Key Takeaways

1. **Never confuse spatial and radiometric scales**
2. **COG pixel_scale = spatial resolution (meters/pixel)**
3. **STAC scale = radiometric calibration (unitless factor)**
4. **Different sources, different purposes, different storage**
5. **Both are important but serve completely different functions**

This clarification ensures we extract and use the correct scale values for their intended purposes!
