# COG Metadata Field Mapping & Documentation

## Overview
This document provides comprehensive mapping between async-tiff extracted fields and our Delta Lake schema, with detailed explanations of what each field represents and when values should be NULL vs actual data.

## Core Principles

### 1. No Default Values
**CRITICAL**: We never use default/assumed values. If a field is not present in the COG header, we store NULL in Delta Lake.

### 2. Machine-Readable Storage
**OPTIMIZATION**: We store machine-readable numeric codes in Delta Lake for:
- **Storage efficiency**: Integers are smaller than strings
- **Query performance**: Numeric comparisons are faster
- **Data consistency**: No typos or string variations
- **Future-proofing**: Mappings can be updated independently
- **Schema evolution**: Easy to add new fields without breaking existing data

### 3. Separate Human-Readable Mappings
Human-readable descriptions are provided via lookup tables in `tiff_mappings.py` for:
- User interfaces and reporting
- Data analysis and visualization
- Documentation and debugging

## Field Mapping Table

| Delta Lake Field | async-tiff Source | TIFF Tag | Description | When NULL | Data Type | Human-Readable |
|------------------|-------------------|----------|-------------|-----------|-----------|----------------|
| `cog_width` | `first_ifd.image_width` | 256 | Image width in pixels | Never (required) | int | Same |
| `cog_height` | `first_ifd.image_height` | 257 | Image height in pixels | Never (required) | int | Same |
| `cog_tile_width` | `first_ifd.tile_width` | 322 | Tile width in pixels | If not tiled | int | Same |
| `cog_tile_height` | `first_ifd.tile_height` | 323 | Tile height in pixels | If not tiled | int | Same |
| `cog_dtype_code` | `first_ifd.sample_format.value` | 339 | Sample format code | Never (required) | int | 1=uint, 2=int, 3=float |
| `cog_bits_per_sample` | `first_ifd.bits_per_sample[0]` | 258 | Bit depth | Never (required) | int | 8, 16, 32, 64 |
| `cog_compression_code` | `first_ifd.compression` | 259 | Compression method code | Never (1=none if uncompressed) | int | 1=none, 5=lzw, 8=deflate |
| `cog_predictor` | `first_ifd.predictor` | 317 | TIFF predictor for compression | If not using prediction | int | 1=none, 2=horizontal, 3=float |
| `cog_crs_code` | `geo_key_directory.projected_type` | GeoKey 3072 | EPSG code for CRS | If no CRS info | int | 32612=UTM 12N, 4326=WGS84 |
| `cog_transform` | `model_pixel_scale` + `model_tiepoint` | 33550 + 33922 | 6-element affine transform | If no geospatial info | array<double> | Same |
| `cog_tile_offsets` | `first_ifd.tile_offsets` | 324 | Byte offsets of each tile | If not tiled | array<bigint> | Same |
| `cog_tile_byte_counts` | `first_ifd.tile_byte_counts` | 325 | Byte counts of each tile | If not tiled | array<bigint> | Same |
| `cog_dn_scale` | `raster:bands[0].scale` or asset `scale` | STAC only | Radiometric scale factor (DN = scale * raw + offset) | If no scale info | double | Same |
| `cog_dn_offset` | `raster:bands[0].offset` or asset `offset` | STAC only | Radiometric offset value (DN = scale * raw + offset) | If not specified | double | Same |

## Detailed Field Documentation

### Core Image Properties

#### `cog_width` & `cog_height`
- **TIFF Tags**: 256 (ImageWidth), 257 (ImageLength)
- **Description**: Fundamental image dimensions in pixels
- **Never NULL**: These are required TIFF fields
- **Source**: `first_ifd.image_width`, `first_ifd.image_height`

#### `cog_tile_width` & `cog_tile_height`
- **TIFF Tags**: 322 (TileWidth), 323 (TileLength)
- **Description**: Tile dimensions for tiled TIFFs
- **NULL When**: Image uses strips instead of tiles
- **COG Requirement**: COGs must be tiled, so these should never be NULL for valid COGs
- **Source**: `first_ifd.tile_width`, `first_ifd.tile_height`

### Data Type & Format

#### `cog_dtype_code` & `cog_bits_per_sample`
- **TIFF Tags**: 339 (SampleFormat) + 258 (BitsPerSample)
- **Description**: Pixel data type as separate numeric codes
- **Never NULL**: Required for data interpretation
- **Storage**: Machine-readable numeric codes
- **Mapping**:
  - dtype_code=1, bits_per_sample=8 → "uint8"
  - dtype_code=1, bits_per_sample=16 → "uint16"
  - dtype_code=3, bits_per_sample=32 → "float32"

#### `cog_compression_code`
- **TIFF Tag**: 259 (Compression)
- **Description**: Compression method as numeric code
- **Never NULL**: Defaults to 1 (none) if uncompressed
- **Storage**: Machine-readable numeric code
- **Mapping**:
  - 1 = "none"
  - 5 = "lzw"
  - 7 = "jpeg"
  - 8 = "deflate"
  - 32773 = "packbits"

#### `cog_predictor`
- **TIFF Tag**: 317 (Predictor)
- **Description**: Predictor used with compression
- **NULL When**: No predictor used (most common)
- **Values**: 1 (none), 2 (horizontal), 3 (floating point)

### Geospatial Properties

#### `cog_crs_code`
- **GeoTIFF Tag**: GeoKey 3072 (ProjectedCSTypeGeoKey) or 2048 (GeographicTypeGeoKey)
- **Description**: EPSG code for coordinate reference system
- **NULL When**: No CRS information in GeoTIFF tags
- **Format**: Numeric integer (e.g., 32612 for UTM Zone 12N)

#### `cog_transform`
- **TIFF Tags**: 33550 (ModelPixelScaleTag) + 33922 (ModelTiepointTag)
- **Description**: 6-element affine transformation matrix
- **Format**: [scale_x, shear_x, shear_y, scale_y, translate_x, translate_y]
- **NULL When**: No geospatial transformation information
- **Calculation**: Derived from pixel scale and tiepoint

#### `cog_dn_scale` & `cog_dn_offset`
- **Sources**:
  - STAC Metadata: `raster:bands[0].scale` and `raster:bands[0].offset`
  - Asset Properties: `scale` and `offset` fields
- **Description**: Radiometric calibration factors for converting raw pixel values
- **Formula**: `DN = cog_dn_scale * raw_value + cog_dn_offset`
- **NULL When**: No scale/offset information in STAC metadata
- **NOT from COG headers**: COG pixel_scale is spatial resolution (meters/pixel), not radiometric scaling
- **Use Case**: Converting raw pixel values to calibrated digital numbers or physical units
- **Example**: `"scale": 0.001, "offset": 0` converts raw values to reflectance

### Tile Structure

#### `cog_tile_offsets`
- **TIFF Tag**: 324 (TileOffsets)
- **Description**: Byte offsets of each tile in the file
- **NULL When**: Image uses strips instead of tiles
- **COG Requirement**: Should never be NULL for valid COGs

#### `cog_tile_byte_counts`
- **TIFF Tag**: 325 (TileByteCounts)
- **Description**: Compressed byte size of each tile
- **NULL When**: Image uses strips instead of tiles
- **COG Requirement**: Should never be NULL for valid COGs

## Important: Two Different Types of "Scale"

### ⚠️ CRITICAL DISTINCTION: Spatial vs Radiometric Scale

| Scale Type | Source | Purpose | Field Name | Example Value |
|------------|--------|---------|------------|---------------|
| **Spatial Scale** | COG headers (pixel_scale) | Spatial resolution (meters/pixel) | `cog_transform` | 10.0 (10m/pixel) |
| **Radiometric Scale** | STAC metadata (scale/offset) | Pixel value calibration | `cog_dn_scale` | 0.001 (reflectance) |

**DO NOT CONFUSE THESE!**
- **COG pixel_scale**: Geographic/spatial resolution
- **STAC scale**: Radiometric calibration for pixel values

## STAC vs COG Field Name Differences

### Why Different Names?

1. **STAC Asset Level**: STAC describes assets at a higher level
2. **COG Header Level**: COG headers contain low-level TIFF technical details
3. **Namespace Clarity**: `cog_` prefix distinguishes from STAC asset properties

### Common Confusions

| STAC Term | COG Term | Reason for Difference |
|-----------|----------|----------------------|
| `eo:bands` | `samples_per_pixel` | STAC describes spectral bands, COG describes data samples |
| `proj:epsg` | `cog_crs_code` | STAC uses full EPSG format, COG stores numeric code |
| `proj:transform` | `cog_transform` | STAC may have different transform than actual COG |
| Asset `type` | `cog_compression_code` | STAC describes MIME type, COG describes compression |

## Validation Rules

### Required Fields (Never NULL)
- `cog_width`, `cog_height`: Fundamental image properties
- `cog_dtype_code`, `cog_bits_per_sample`: Required for data interpretation
- `cog_compression_code`: Always present (1 if uncompressed)

### COG-Specific Requirements
- `cog_tile_width`, `cog_tile_height`: Must be present for valid COGs
- `cog_tile_offsets`, `cog_tile_byte_counts`: Must be present for valid COGs

### Optional Fields (Can be NULL)
- `cog_predictor`: Only present when using compression with prediction
- `cog_crs_code`: Only present in GeoTIFFs
- `cog_transform`: Only present in georeferenced images
- `cog_dn_scale`, `cog_dn_offset`: Only present when specified in STAC metadata

## Implementation Notes

### async-tiff Advantages
1. **Direct Access**: No HTTP range request parsing
2. **Type Safety**: Rust enums for compression, sample format
3. **Performance**: Native Rust TIFF parsing (3.17x faster)
4. **Completeness**: Access to all TIFF tags and GeoKeys
5. **HTTP/2**: Better connection reuse through obstore
6. **Store Reuse**: Efficient batch processing with shared stores

### Handling Missing Values
```python
# CORRECT: Check if field exists, store NULL if not
predictor = getattr(first_ifd, 'predictor', None)
if predictor is not None:
    cog_predictor = int(predictor)
else:
    cog_predictor = None  # Store NULL in Delta Lake

# INCORRECT: Never use defaults
cog_predictor = getattr(first_ifd, 'predictor', 1)  # DON'T DO THIS
```

### Verified NULL Handling
Our implementation correctly handles NULL values:
- **`cog_dn_offset`**: NULL when not specified in STAC metadata (never defaults to 0.0)
- **`cog_dn_scale`**: NULL when not specified in STAC metadata
- **`cog_predictor`**: NULL when no compression predictor used
- **`cog_crs_code`**: NULL when no geospatial information present
- **`cog_transform`**: NULL when missing pixel_scale OR tiepoint

### Machine-Readable Storage with Human-Readable Mappings
We store machine-readable numeric codes in Delta Lake:
- **`cog_compression_code`**: 8 (stored in Delta Lake)
- **Human-readable mapping**: 8 → "deflate" (via lookup tables)

This provides optimal storage efficiency with on-demand human-readable conversion.

## References
- [TIFF 6.0 Specification](https://www.adobe.io/content/dam/udp/en/open/standards/tiff/TIFF6.pdf)
- [GeoTIFF Format Specification](https://docs.ogc.org/is/19-008r4/19-008r4.html)
- [COG Specification](https://cogeo.org/)
- [async-tiff Documentation](https://developmentseed.org/async-tiff/)
