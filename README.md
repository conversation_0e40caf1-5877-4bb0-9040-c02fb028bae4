# Data Marketplace Internal

STAC + COG ingestion system with Delta Lake storage and async COG header parsing.

## Overview

This system ingests STAC collections into a unified Delta Lake table where each COG asset gets its own row with complete scene and asset metadata. The design enables cross-collection spatial and temporal queries.

### Key Features

- **Unified Table Design**: One row per COG asset with scene + asset metadata
- **Async COG Header Parsing**: High-performance async-tiff and custom parsers
- **Auto-Tuned Performance**: Automatically scales with CPU cores (no manual tuning)
- **Delta Lake Storage**: ACID transactions, optimization, and cleanup operations
- **Streaming Architecture**: Producer-consumer pattern with batch processing
- **Force Reingest**: MERGE mode for overwriting existing data

## Architecture

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   STAC APIs     │───▶│ Streaming STAC   │───▶│   Delta Lake    │
│ (Earth Search)  │    │ + COG Processor  │    │ Unified Table   │
│                 │    │ - Auto-tuned     │    │ - 1 row per     │
└─────────────────┘    │ - Async-tiff     │    │   COG asset     │
                       │ - Batch proc.    │    │ - Optimized     │
                       │ - 20 consumers   │    │ - Cleaned up    │
                       └──────────────────┘    └─────────────────┘
```

## Quick Start

### Installation

```bash
cd data-marketplace-internal
uv sync
```

### Basic Ingestion (Auto-Tuned)

```bash
# Simple ingestion - concurrency auto-tuned based on CPU cores
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --output-path "s3://your-bucket/unified_stac_table" \
    --datetime-range "2024-01-01/2024-01-31" \
    --max-items 1000
```

### Advanced Options

```bash
# Force reingest with custom cleanup settings
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --output-path "s3://your-bucket/unified_stac_table" \
    --datetime-range "2024-01-01/2024-01-31" \
    --force-reingest \
    --vacuum-retention-hours 168 \
    --batch-size 2000
```

### Performance Testing

```bash
# Test end-to-end performance with different parsers
uv run python test_end_to_end_performance.py

# Test null value handling
uv run python tests/test_null_value_handling.py
```

### Programmatic Usage

```python
from data_marketplace.ingestion.delta_stac_ingester import DeltaStacIngester

# Initialize ingester (auto-tuned for your CPU cores)
ingester = DeltaStacIngester(
    unified_table_path="s3://your-bucket/unified_table",
    storage_options={"AWS_REGION": "us-west-2"}
)

# Ingest with auto-tuned concurrency
results = await ingester.ingest_stac_collection(
    stac_api_url="https://earth-search.aws.element84.com/v1",
    collection_id="sentinel-2-l2a",
    max_items=1000,
    datetime_range="2024-01-01/2024-01-31"
    # concurrency parameters auto-tuned based on CPU cores
)
```

## Components

### 1. COG Header Parsing
- **AsyncTiffStacCogProcessor**: High-performance async-tiff based parser (5x faster)
- **AsyncCOGHeaderParser**: Custom aiohttp-based parser (fallback)
- **Auto-selection**: Automatically chooses best parser for performance
- **Batch processing**: Processes 16 COG assets per batch for optimal throughput

### 2. Streaming Architecture (`src/data_marketplace/ingestion/streaming_processor.py`)
- **Auto-tuned concurrency**: Scales with CPU cores (20 cores → 20 consumers)
- **Producer-consumer pattern**: Efficient memory usage with bounded buffers
- **Batch processing**: Optimized batch sizes for async-tiff performance
- **Error handling**: Graceful failure handling with detailed logging

### 3. Delta Lake Management
- **DeltaStacIngester**: Production-ready unified ingestion
- **Automatic optimization**: Compacts small files into larger ones
- **Vacuum cleanup**: Removes orphan files with configurable retention
- **APPEND/MERGE modes**: Fast append or force reingest with deduplication

## Performance

### Real AWS Performance (4-core VM, same region as S3)
- **Processing Rate**: **280.95 items/sec** (production AWS environment)
- **Batch Size**: 5,000 items, ~2GB RAM per batch
- **1 Day Sentinel-2**: 305,792 items in 18.1 minutes
- **Auto-tuned**: 4 consumers, 80 COG concurrency (scales with CPU cores)

### Scaling Projections
- **1 Year Sentinel-2**: ~111 million items
- **Estimated Time**: **4.6 days** on 4-core VM (280 items/sec)
- **Memory Usage**: 2-4GB RAM (depending on batch size)
- **Optimization**: Larger batches (10,000) recommended for better Delta Lake performance

### Cleanup Operations
- **Table optimization**: Compacts 85 small files → 6 large files (93% reduction)
- **Vacuum cleanup**: Removes orphan files safely (79 files deleted in 11.3s)
- **Storage savings**: ~711MB saved in production testing

### Schema (50 fields)
```
Scene Metadata (repeated per COG asset):
├── scene_id, collection, datetime, platform
├── geometry, bbox (4-field struct)
├── Temporal partitioning: year, month

COG Asset Metadata (asset-specific):
├── cog_key, cog_href, cog_title, cog_roles
├── Dimensions: cog_width, cog_height, cog_tile_width, cog_tile_height
├── Technical: cog_dtype, cog_compression, cog_predictor, cog_crs
├── Scale/offset: cog_dn_scale, cog_dn_offset
└── Arrays: cog_tile_offsets, cog_tile_byte_counts
```

## CLI Options

### Auto-Tuned Parameters (No Manual Configuration Needed)
- **Concurrency**: Automatically scales with CPU cores
- **Buffer sizes**: Calculated based on batch size and consumer count
- **COG requests**: 20 requests per CPU core for I/O-bound operations
- **STAC processing**: Conservative scaling for CPU-bound operations

### Available Options
```bash
# Core parameters
--output-path          # S3 or local path for Delta table
--datetime-range       # ISO 8601 date range (2024-01-01/2024-01-31)
--max-items           # Limit number of STAC items (for testing)
--batch-size          # Items per batch (default: 2000)

# Cleanup options
--force-reingest      # Use MERGE mode instead of APPEND
--vacuum-retention-hours  # Hours to retain old files (default: 168 = 7 days)
--skip-vacuum         # Skip vacuum cleanup
--skip-optimize       # Skip table optimization

# Utility
--log-level           # DEBUG, INFO, WARNING, ERROR
```

## Production Deployment

### AWS Production Performance

**4-core VM (c6i.xlarge) Results:**
- **280.95 items/sec** - Real production performance
- **305,792 items in 18.1 minutes** (1 day of Sentinel-2)
- **Memory**: 2GB per 5,000-item batch
- **Auto-tuning**: 4 consumers, 80 COG concurrency

**Scaling Recommendations:**

| Instance Type | vCPUs | Expected Performance | 1 Year Sentinel-2 |
|---------------|-------|---------------------|-------------------|
| c6i.xlarge    | 4     | 280 items/sec      | 4.6 days         |
| c6i.2xlarge   | 8     | ~560 items/sec     | 2.3 days         |
| c6i.4xlarge   | 16    | ~1,120 items/sec   | 1.2 days         |
| c6i.8xlarge   | 32    | ~2,240 items/sec   | 14 hours         |

**Optimization Recommendations:**
- **Increase batch size to 10,000** for better Delta Lake performance
- **Use same AWS region** as S3 bucket (critical for performance)
- **Monitor memory usage** - expect 2-4GB per batch
- **Delta writes are bottleneck** - larger batches help significantly

### Production Examples

```bash
# Basic production ingestion (auto-tuned)
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --output-path "s3://your-bucket/unified_stac_table" \
    --datetime-range "2024-01-01/2024-12-31"

# Optimized for AWS production (recommended batch size)
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --output-path "s3://your-bucket/unified_stac_table" \
    --datetime-range "2024-01-01/2024-12-31" \
    --batch-size 10000 \
    --vacuum-retention-hours 168

# Force reingest with aggressive cleanup
uv run scripts/ingest_stac_unified.py \
    "https://earth-search.aws.element84.com/v1" \
    "sentinel-2-l2a" \
    --output-path "s3://your-bucket/unified_stac_table" \
    --datetime-range "2024-01-01/2024-01-31" \
    --force-reingest \
    --vacuum-retention-hours 24
```

## Troubleshooting

### Common Issues

**Slow Performance:**
- Check CPU utilization (should be high during COG parsing)
- Verify network connectivity to S3
- Monitor Delta Lake write times (main bottleneck)
- Consider increasing batch size for better Delta performance

**Memory Issues:**
- Reduce batch size if memory usage is high
- Monitor with `htop` or system monitoring
- Auto-tuning prevents most memory issues

**No Output Files:**
- Check S3 permissions for write access
- Verify first batch completed successfully
- Check logs for error messages

### Performance Monitoring

```bash
# Monitor system resources
htop                    # CPU and memory usage
iotop                   # Disk I/O
nethogs                 # Network usage per process

# Check ingestion progress
tail -f logs/ingestion.log | grep "records/sec"
```

## Implementation Status

### Completed Features
- [x] **Async COG Header Parsing** - Both async-tiff and custom parsers
- [x] **Auto-Tuned Performance** - Scales with CPU cores automatically
- [x] **Streaming Architecture** - Producer-consumer with batch processing
- [x] **Delta Lake Integration** - ACID transactions with optimization
- [x] **Table Cleanup** - Vacuum and optimize operations
- [x] **Force Reingest** - MERGE mode for overwriting data
- [x] **Comprehensive Testing** - Performance and null value tests
- [x] **Production CLI** - Simple interface with smart defaults

### Performance Achievements
- **280 items/sec** on 4-core AWS VM (production environment)
- **19x faster** than laptop testing (real AWS vs local performance)
- **4.6 days** to ingest 1 year of Sentinel-2 data (4-core VM)
- **Auto-tuning** scales perfectly with CPU cores (no manual configuration)
- **93% file reduction** through table optimization (85 → 6 files)

### Architecture Highlights
- **First principles optimization** based on CPU cores and I/O patterns
- **Streaming processor** with bounded buffers and error handling
- **Delta Lake best practices** with proper cleanup and optimization
- **Production ready** with comprehensive logging and monitoring

## License

Terrafloww Labs Proprietary
